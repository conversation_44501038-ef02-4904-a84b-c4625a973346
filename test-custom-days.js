/**
 * Test extractCustomDaysFromCron function
 */

function extractCustomDaysFromCron(cronExpression) {
  if (!cronExpression || typeof cronExpression !== "string") {
    return [];
  }

  const parts = cronExpression.trim().split(/\s+/);
  if (parts.length !== 5) {
    return [];
  }

  const dayOfWeekField = parts[4]; // 5th field is day of week
  
  if (!dayOfWeekField || dayOfWeekField === "*") {
    return [];
  }

  // Map cron day names to full day names
  const dayMap = {
    'sun': 'Sunday',
    'mon': 'Monday',
    'tue': 'Tuesday',
    'wed': 'Wednesday',
    'thu': 'Thursday',
    'fri': 'Friday',
    'sat': 'Saturday',
    '0': 'Sunday',
    '1': 'Monday',
    '2': 'Tuesday',
    '3': 'Wednesday',
    '4': 'Thursday',
    '5': 'Friday',
    '6': 'Saturday'
  };

  const customDays = [];
  const dayParts = dayOfWeekField.split(',');

  for (const dayPart of dayParts) {
    const day = dayPart.trim().toLowerCase();
    if (dayMap[day]) {
      customDays.push(dayMap[day]);
    }
  }

  return customDays;
}

// Test cases from your data
const testCases = [
  {
    repeat_type: "custom",
    cron: "* 0-6 10,11 4 mon,wed,fri",
    expected: ["Monday", "Wednesday", "Friday"]
  },
  {
    repeat_type: "custom", 
    cron: "* 1-4,7-11 12,13,14,15,16,17,18 4 mon,wed,fri,thu",
    expected: ["Monday", "Wednesday", "Friday", "Thursday"]
  },
  {
    repeat_type: "custom",
    cron: "* 0-23 29 4 mon",
    expected: ["Monday"]
  },
  {
    repeat_type: "custom",
    cron: "* 0-23 23,24,25,26,27 4 sun,fri",
    expected: ["Sunday", "Friday"]
  },
  {
    repeat_type: "custom",
    cron: "* 0-23 1,2,3,4,5 4 sun,mon,thu,fri,sat",
    expected: ["Sunday", "Monday", "Thursday", "Friday", "Saturday"]
  },
  {
    repeat_type: "daily",
    cron: "* 0-23 9,10 4 *",
    expected: []
  },
  {
    repeat_type: "custom",
    cron: "* 0-23 16,17 7 mon,thu,sat",
    expected: ["Monday", "Thursday", "Saturday"]
  }
];

console.log("Testing extractCustomDaysFromCron function:\n");

testCases.forEach((testCase, index) => {
  const result = extractCustomDaysFromCron(testCase.cron);
  const customDays = testCase.repeat_type === 'custom' ? result : [];
  const passed = JSON.stringify(customDays) === JSON.stringify(testCase.expected);
  
  console.log(`${index + 1}. ${testCase.repeat_type} schedule`);
  console.log(`   Cron: "${testCase.cron}"`);
  console.log(`   Expected: ${JSON.stringify(testCase.expected)}`);
  console.log(`   Result: ${JSON.stringify(customDays)}`);
  console.log(`   Status: ${passed ? '✅ PASS' : '❌ FAIL'}\n`);
});
