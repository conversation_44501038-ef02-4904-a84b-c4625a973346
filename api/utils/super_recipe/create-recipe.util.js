const Joi = require("joi");
const flaverr = require("flaverr");

const validateRecipeInfo = (recipeInfo) => {
  const schema = Joi.object({
    site_id: Joi.string().required(),
    app_type: Joi.string().required(),
    recipe_category: Joi.string().required(),
    run_interval: Joi.number().integer().min(0).max(100).required(),
    components_type: Joi.array().items(Joi.string()).required(),
    template_title: Joi.string().optional(),
    title: Joi.string().required(),
    description: Joi.string().max(500).allow("").optional(),
    notify: Joi.array().items(Joi.string().email()).required(),
    smslist: Joi.array().items(Joi.string()).default([]),
    priority: Joi.number().integer().min(0).max(5).required(),
    recipe_type: Joi.string().valid("action", "alert").required(),
    recipe_template_id: Joi.number().optional(),
    children_recipes: Joi.array()
      .items(
        Joi.object({
          id: Joi.number().optional(),
          execution_order: Joi.number().integer().min(1).required(),
          block_type: Joi.string().valid("if", "else if", "else").required(),
          description: Joi.string().max(500).allow("").optional(),
          expression_template: Joi.string().min(1).default(""),
          params: Joi.object().pattern(Joi.string(), Joi.string()).required(),
          operators: Joi.object().pattern(Joi.string(), Joi.string()).required(),
          observation_time: Joi.number().integer().min(0).required(),
          actions: Joi.when(Joi.ref("/recipe_type"), {
            is: "action",
            then: Joi.array()
              .items(
                Joi.object({
                  id: Joi.number().optional(),
                  command: Joi.string().required(),
                  did: Joi.number().integer().required(),
                  parent: Joi.string().required(),
                  value: Joi.number().required(),
                  execution_order: Joi.number().integer().min(1).required(),
                }),
              )
              .required(),
            otherwise: Joi.forbidden(),
          }),
        }),
      )
      .required(),
  });

  const { error } = schema.validate(recipeInfo, { abortEarly: false });

  if (error) {
    const errorMessages = error.details.map((detail) => detail.message);
    throw flaverr(
      "E_INPUT_VALIDATION",
      new Error(`There were validation errors with your input: ${errorMessages.join(", ")}`),
    );
  }
};

const validateRecipeTemplateInfo = (recipeInfo) => {
  const schema = Joi.object({
    site_id: Joi.string().required(),
    app_type: Joi.string().required(),
    recipe_category: Joi.string().required(),
    run_interval: Joi.number().integer().min(0).max(100).required(),
    components_type: Joi.array().items(Joi.string()).required(),
    template_title: Joi.string().optional(),
    title: Joi.string().required(),
    description: Joi.string().max(500).allow("").optional(),
    notify: Joi.array().items(Joi.string().email()).required(),
    smslist: Joi.array().items(Joi.string()).default([]),
    priority: Joi.number().integer().min(0).max(5).required(),
    recipe_type: Joi.string().valid("action", "alert").required(),
    is_recipe_template: Joi.number().required(),
    recipe_template_id: Joi.number().optional(),
    children_recipes: Joi.array()
      .items(
        Joi.object({
          execution_order: Joi.number().integer().min(1).required(),
          block_type: Joi.string().valid("if", "else if", "else").required(),
          description: Joi.string().max(500).allow("").optional(),
          expression_template: Joi.string().min(1).default(""),
          params: Joi.object().pattern(Joi.string(), Joi.string()).required(),
          operators: Joi.object().pattern(Joi.string(), Joi.string()).required(),
          observation_time: Joi.number().integer().min(0).required(),
          actions: Joi.when(Joi.ref("/recipe_type"), {
            is: "action",
            then: Joi.array()
              .items(
                Joi.object({
                  command: Joi.string().required(),
                  did: Joi.number().integer().required(),
                  parent: Joi.string().required(),
                  value: Joi.number().required(),
                  execution_order: Joi.number().integer().min(1).required(),
                }),
              )
              .required(),
            otherwise: Joi.forbidden(),
          }),
        }),
      )
      .required(),
  });

  const { error } = schema.validate(recipeInfo, { abortEarly: false });

  if (error) {
    const errorMessages = error.details.map((detail) => detail.message);
    throw flaverr(
      "E_INPUT_VALIDATION",
      new Error(`There were validation errors with your input: ${errorMessages.join(", ")}`),
    );
  }
};
module.exports = {
  validateRecipeInfo,
  validateRecipeTemplateInfo,
};
