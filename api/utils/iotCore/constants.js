module.exports = {
  MASTER_CONTROLLER_TYPES: [
    "jouleiocontrol",
    "joulelogger",
    "joulemomo",
    "joulestat",
    "jouleleaf",
    "joulebox",
  ],
  DEFAULT_CONTROLLER_CURRENT_VERSION: "v1.0.1",
  DEFAULT_CONTROLLER_VERSION: "v1.0.1",
  JOB_DEPLOY_CONFIG: {
    defaultJobDescritpion: "IoT Software Update",
    defaultJobIdPrefix: "Software-Update",
    maxTargetLimit: 99,
    presignedUrlConfig: {
      roleArn: "arn:aws:iam::878252606197:role/service-role/aws-iot",
      //  "arn:aws:iam::878252606197:role/service-role/controller-provisioning-role",
      expiresInSec: 3600,
    },
    targetSelection: "SNAPSHOT",
    jobExecutionsRolloutConfig: {
      maximumPerMinute: 100,
      exponentialRate: {
        baseRatePerMinute: 30,
        incrementFactor: 3,
        rateIncreaseCriteria: {
          numberOfSucceededThings: 2,
        },
      },
    },
    abortConfig: {
      criteriaList: [
        {
          action: "CANCEL",
          failureType: "FAILED",
          minNumberOfExecutedThings: 8,
          thresholdPercentage: 100,
        },
      ],
    },
    timeoutConfig: {
      inProgressTimeoutInMinutes: 3600,
    },
  },
  CONTROLLER_UPDATE_STATUS: {
    QUEUE: 0,
    FAIL: 1,
    DONE: 2,
    IN_PROGRESS: 3,
    CANCELLED: 4,
  },
  UPDATE_STATUS: {
    FAILED: 0,
    UPDATE_AVAILABLE: 1,
    UP_TO_DATE: 2,
  },
  SYNC_STATUS: {
    SYNCED: 0,
    SYNC_AVAILABLE: 1,
    SYNC_FAILED: 2,
  },
};
