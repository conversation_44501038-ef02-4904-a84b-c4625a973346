const Joi = require('joi');
const { createError } = require('../globalhelper');

module.exports = {
  fetchDeviceConfig: function(params) {
    const schema = Joi.object({
     controllerId: Joi.string().trim().required(),
     global: Joi.boolean().required(),
     siteId: Joi.string().trim().required(),
   }).unknown(true)

   const { error , value} = schema.validate(params);

   if (error) {
     throw createError({
      message: error.message,
      HTTP_STATUS_CODE:400
    });
   }
   return  value;
 },
 validateSiteId: function(params) {
  const schema = Joi.object({
    siteId: Joi.string().trim().required(),
  }).unknown(true)

  const { error , value} = schema.validate(params);

  if (error) {
    throw createError({
     HTTP_STATUS_CODE:'E_INPUT_VALIDATION', 
     message: error.message
   });
  }
  return  value;
 },
 validateSiteUserIdDeviceId: function(params) {
  const schema = Joi.object({
    siteId: Joi.string().trim().required(),
    userId: Joi.string().trim().required(),
    deviceId: Joi.string().trim().required(),
  }).unknown(true)


  const { error , value} = schema.validate(params);

  if (error) {
    throw createError({
     HTTP_STATUS_CODE:'E_INPUT_VALIDATION', 
     message: error.message
   });
  }
  return  value;
 },
 validateDevConfigStateChangeInputs: function(params) {
  const schema = Joi.object({
    siteId: Joi.string().trim().required(),
    userId: Joi.string().trim().required(),
    deviceId: Joi.string().trim().optional(),
  }).unknown(true)

  const { error , value} = schema.validate(params);

  if (error) {
    throw createError({
     HTTP_STATUS_CODE:'E_INPUT_VALIDATION', 
     message: error.message
   });
  }
  return  value;
 },
 validateSiteAndDeviceId: function(params) {
  const schema = Joi.object({
    siteId: Joi.string().trim().required(),
    deviceId: Joi.string().trim().required(),
  }).unknown(true)


  const { error , value} = schema.validate(params);

  if (error) {
    throw createError({
     HTTP_STATUS_CODE:'E_INPUT_VALIDATION', 
     message: error.message
   });
  }
  return  value;
 },
 validateSiteIdAndUserId: function(params) {
  const schema = Joi.object({
    siteId: Joi.string().trim().required(),
    userId: Joi.string().trim().required(),
  }).unknown(true)

  const { error , value} = schema.validate(params);

  if (error) {
    throw createError({
     HTTP_STATUS_CODE:'E_INPUT_VALIDATION', 
     message: error.message
   });
  }
  return  value;
 },
 validateSiteIdUserIdAndDeviceId: function(params) {
  const schema = Joi.object({
    siteId: Joi.string().trim().required(),
    userId: Joi.string().trim().required(),
    deviceId: Joi.string().trim().required(),
  }).unknown(true)

  const { error , value} = schema.validate(params);

  if (error) {
    throw createError({
     HTTP_STATUS_CODE:'E_INPUT_VALIDATION', 
     message: error.message
   });
  }
  return  value;
 },
}