const globalhelper = require("../globalhelper");


module.exports = {
  appendFeedbackPort,
  filterParameters
};


function filterParameters(param, devParamMap, devFeedbackMap) {
    if (param.hasOwnProperty("regType")) {
      param.regType = param.regType?.toString();
    }
    const {
      filter_oldVal,
      filter_variance,
      filter_existence,
      deviceId,
      abbr,
      utilityType,
      driver,
      max,
      min,
      index,
      mulFactor,
      regType,
      offset,
      bitIndex,
      secDriver,
      decimalPrecision
    } = param;

    param.filter = {
      oldVal: filter_oldVal,
      variance: filter_variance,
      existence: filter_existence
    };

    param.properties = {
      driver,
      max,
      min,
      index,
      regType,
      mulFactor,
      offset: offset?.toString(),
      bitIndex,
      secDriver
    };


    if (decimalPrecision) {
      param.decimalPrecision = Number.parseInt(decimalPrecision);
    }

    if (utilityType === 'command') {
      const key = `${deviceId}_${abbr}`;
      if (devFeedbackMap[key]) {
        param.feedbackPort = devFeedbackMap[key];
      }
    }

    [
      'driver', 'max', 'min', 'index', 'mulFactor', 'offset',
      'bitIndex', 'deviceId_abbr', 'filter_existence', 'filter_oldVal',
      'filter_variance', 'siteId', 'abbr', 'secDriver'
    ].forEach(k => delete param[k]);

    if (!devParamMap[deviceId]) devParamMap[deviceId] = {};
    devParamMap[deviceId][abbr] = param;
}

function appendFeedbackPort(component, devFeedbackMap) {
    const { controls } = component;
    if (!controls) return;

    for (const cp of controls) {
      const { deviceId, min, max, unit, timeout, key, device_abbr } = cp;
      let { expression } = cp;

      const feedbackPortConfig = {
        expression,
        min,
        max,
        unit,
        timeout,
        deviceId: globalhelper.parseFormulaToIds(expression)
      };

      expression = expression.replace(/\|\|/g, ' ');
      expression = expression.substring(1, expression.length - 1);
      feedbackPortConfig.expression = expression.replace(/@/g, '.');

      const mapKey = device_abbr ? `${deviceId}_${device_abbr}` : `${deviceId}_${key}`;
      devFeedbackMap[mapKey] = feedbackPortConfig;
    }
}

