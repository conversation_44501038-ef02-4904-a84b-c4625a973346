const Joi = require("joi");
const { createError } = require("../globalhelper");
const { DEV_CONFIG_STATUS } = require("./Constants");

module.exports = {
  throwExceptionDeviceNotFound,
  throwExceptionInvalidController,
  throwExceptionDevConfigNotFound,
  throwExceptionUnableToPublishIOT,
  throwExceptionControllerNotConfigured,
  throwExceptionNotAController,
  throwExceptionServerError,
  validateDevConfigFeedback,
  throwExceptionInvalidDevConfigState
};

function throwExceptionDeviceNotFound(deviceId) {
  throw createError({
    message: 'Device not found with id: ' + deviceId,
    HTTP_STATUS_CODE: 404
  });
}

function throwExceptionInvalidController() {
  throw createError({
    message: 'Not a controller',
    HTTP_STATUS_CODE: 422
  });
}

function throwExceptionDevConfigNotFound(requestId) {
  throw createError({
    message: 'Device configuration not found for request: ' + requestId,
    HTTP_STATUS_CODE: 404
  });
}

function throwExceptionUnableToPublishIOT(topic, data) {
  throw createError({
    message: 'Unable to publish to IoT topic: ' + topic,
    data: data,
    HTTP_STATUS_CODE: 500
  });
}

function throwExceptionControllerNotConfigured(controllerId) {
  throw createError({
    message: 'Controller not found: ' + controllerId,
    HTTP_STATUS_CODE: 404
  });
}

function throwExceptionNotAController() {
  throw createError({
    message: 'Not a controller',
    HTTP_STATUS_CODE: 422
  });
}

function throwExceptionServerError(error) {
  throw createError({
    message: 'We encountered an error while processing your request: ' + error + '. Please try again later or contact support if the issue persists.',
    HTTP_STATUS_CODE: 500
  });
}

function validateDevConfigFeedback(payload) {
  var devConfigFeedbackSchema = Joi.object({
    siteId: Joi.string().required().trim().messages({
      'string.empty': 'Site ID is required',
      'any.required': 'Site ID is required'
    }),
    controllerId: Joi.string().required().trim().messages({
      'string.empty': 'Controller ID is required',
      'any.required': 'Controller ID is required'
    }),
    status: Joi.number().required().integer().valid(
      0, 
      1
    ).messages({
      'number.base': 'Status must be a number',
      'any.required': 'Status is required',
      'any.only': 'Status must be one of: SUCCESS(1), FAILED(0)'
    }),
    requestId: Joi.string().optional().trim().messages({
      'string.empty': 'Request ID cannot be empty'
    })
  });

  var validationResult = devConfigFeedbackSchema.validate(payload, {
    abortEarly: false,
    stripUnknown: true
  });

  if (validationResult.error) {
    var errorMessage = validationResult.error.details.map(function(detail) {
      return detail.message;
    }).join(', ');
    throw createError({
      message: errorMessage,
      HTTP_STATUS_CODE: 400
    });
  }

  return validationResult.value;
}

function throwExceptionInvalidDevConfigState(siteId, controllerId) {
  throw createError({
    message: `DevConfig is not in the 'in-progress' state for site: ${siteId}, controller: ${controllerId}`,
    HTTP_STATUS_CODE: 409
  });
}