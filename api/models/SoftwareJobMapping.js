module.exports = {
  datastore: "postgres",
  tableName: "software_job_mappings",
  primaryKey: "id",

  attributes: {
    id: {
      type: "number",
      autoIncrement: true,
    },
    siteId: {
      columnName: "site_id",
      type: "string",
      required: true,
    },
    status: {
      type: "number",
      isIn: [0, 1, 2, 3, 4], // 0=queue, 1=fail, 2=done, 3=in-progress, 4=cancelled
    },
    awsJobId: {
      columnName: "aws_job_id",
      type: "string",
      required: true,
    },
    version: {
      type: "string",
    },
    userJobIdRef: {
      columnName: "user_job_id_ref",
      type: "number",
      required: true,
    },
    createdAt: {
      columnName: "created_at",
      type: "ref",
      columnType: "timestamp",
      autoCreatedAt: true,
    },
    updatedAt: {
      columnName: "updated_at",
      type: "ref",
      columnType: "timestamp",
      autoUpdatedAt: true,
    },
  },
};
