// api/models/DevConfig.js

module.exports = {
  datastore: 'postgres',
  tableName: 'dev_config',
  attributes: {
    id: {
      type: 'string',
      columnName: 'id',
      autoIncrement: true,
    },
    createdAt: {
      type: 'ref',
      autoCreatedAt: true
    },
    updatedAt: {
      type: 'ref',
      autoUpdatedAt: true
    },
    status: {
      type: 'number',
      isIn: [0, 1, 2, 3], // 0: queued, 1: in-progress, 2: failed, 3: success
      description: 'Status of the config: 0=queued, 1=in-progress, 2=failed, 3=success',
    },
    controllerId: {
      type: 'string',
      columnName: 'controller_id',
      allowNull: true
    },
    syncedBy: {
      type: 'string',
      columnName: 'synced_by',
      allowNull: true
    },
    siteId: {
      type: 'string',
      columnName: 'site_id',
      allowNull: true
    },
    lastSyncedAt: {
      type: 'ref',
      columnName: 'last_synced_at',
      description: 'Last time when dev config synced successfully.'
    }
  }
};
