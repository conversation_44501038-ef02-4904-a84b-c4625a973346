module.exports = {
  datastore: process.env.SMART_ALERT_DB_NAME,
  tableName: 'alert_template',
  primaryKey: 'id',

  attributes: {
    id: {
      type: 'number',
      autoIncrement: true,
    },
    name: {
      type: 'string',
      required: true,
    },
    description: {
      type: 'string',
      columnType: 'text',
    },
    observer_source: {
      type: 'string',
      example: 'FLUX',
      isIn: ['FLUX', 'TICK', 'OTHER', 'CPA', 'recipe', 'iot_health', 'super_recipe'],
      description: 'this column contains the source of observation for e.g. FLUX means observations is happening because of flux task same for TICK. OTHER is usually used when observation is happening at low level side e.g. code base'
    },
    observer_execution_ref_id: {
      type: 'string',
      description: 'this can be reference of flux task id'
    },
    template_category: {
      type: 'number',
      isIn: [1, 2],
      defaultsTo: 2,
      description: '1: global, 2: local'
    },
    alert_category: {
      type: 'string',
      isIn: ['recipe', 'cpa', 'iot_health'],
      defaultsTo: 'cpa',
    },
    severity: {
      type: 'string',
      isIn: ['low', 'medium', 'high', 'critical'],
      required: true,
    },
    status: {
      type: 'number',
      defaultsTo: 1,
      isIn: [0, 1, 2],
      description: '0: Deleted/Inactive, 1: Active, 2: Paused'
    },
    misc: {
      type: 'json',
      defaultsTo: {}
    },
    created_by: {
      type: 'string',
    },
    updated_by: {
      type: 'string',
    },
    createdAt: {
      type: 'ref',
      columnType: 'timestamp',
      autoCreatedAt: true,
    },
    updatedAt: {
      type: 'ref',
      columnType: 'timestamp',
      autoUpdatedAt: true,
    },
    config: {
      type: 'json'
    },
    expression: {
      type: 'string'
    },
    escalation_time_in_min: {
      type: 'number',
    },
    escalated_to: {
      type: 'json',
      defaultsTo: [],
    },
    site_id: {
      type: 'string',
      defaultsTo: 'dejoule',
    }
  },
};
