module.exports = {
  datastore: process.env.SMART_ALERT_DB_NAME,
  tableName: 'notification_message_template',
  primaryKey: 'id',

  attributes: {
    id: {
      type: 'number',
      autoIncrement: true,
    },
    alert_template_ref_id: {
      type: 'number',
      required: true
    },
    channel: {
      type: 'string',
      enum: ['email', 'sms', 'whatsapp'],
      required: true
    },
    title: {
      type: 'string',
      columnType: 'text',
      required: true
    },
    description: {
      type: 'string',
      columnType: 'text',
      required: true
    },
    template_id: {
      type: 'string',
    },
    created_by: {
      type: 'string',
      required: true
    },
    createdAt: {
      type: 'ref',
      columnType: 'timestamp',
      autoCreatedAt: true
    },
    updatedAt: {
      type: 'ref',
      columnType: 'timestamp',
      autoCreatedAt: true
    },
    status: {
      type: 'number',
      defaultsTo: 1,
      isIn: [0, 1, 2],
      description: '0: Inactive, 1: Active, 2: Paused'
    },
    event_type: {
      type: 'string',
      defaultsTo: 'OCCURRED',
    },
    alert_inventory_ref_id: {
      type: 'number',
    },
  }
};
