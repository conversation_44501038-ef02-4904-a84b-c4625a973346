module.exports = {
  datastore: "postgres",
  tableName: "controller_update_status",
  primaryKey: "id",

  attributes: {
    id: {
      type: "number",
      autoIncrement: true,
    },
    siteId: {
      type: "string",
      columnName: "site_id",
      required: true,
    },
    currentVersion: {
      type: "string",
      columnName: "current_version",
      required: true,
    },
    expectedVersion: {
      type: "string",
      columnName: "expected_version",
      required: true,
    },
    softwareJobMappingId: {
      type: "number",
      columnName: "software_job_mapping_id",
      required: true,
    },
    controllerId: {
      type: "number",
      columnName: "controller_id",
      required: true,
    },
    status: {
      type: "number",
      isIn: [0, 1, 2, 3, 4], // 0=queue, 1=fail, 2=done, 3=in-progress, 4=cancelled
    },
    createdAt: {
      columnName: "created_at",
      type: "ref",
      columnType: "timestamp",
      autoCreatedAt: true,
    },
    updatedAt: {
      columnName: "updated_at",
      type: "ref",
      columnType: "timestamp",
      autoUpdatedAt: true,
    },
    controllerType: {
      type: "string",
      columnName: "controller_type",
      required: true,
    },
  },
};
