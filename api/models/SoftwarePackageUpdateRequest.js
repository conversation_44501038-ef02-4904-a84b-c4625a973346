module.exports = {
  datastore: "postgres",
  tableName: "software_package_update_request",
  primaryKey: "id",

  attributes: {
    id: {
      type: "number",
      autoIncrement: true,
    },
    userGeneratedJobId: {
      columnName: "user_generated_job_id",
      type: "string",
      required: true,
    },
    createdBy: {
      columnName: "created_by",
      type: "string",
      required: true,
    },
    createdAt: {
      columnName: "created_at",
      type: "ref",
      columnType: "timestamp",
      autoCreatedAt: true,
    },
    updatedAt: {
      columnName: "updated_at",
      type: "ref",
      columnType: "timestamp",
      autoUpdatedAt: true,
    },
  },
};
