module.exports = {
  friendlyName: " Global Param Formatter",
  description: "",
  sync: true,
  inputs: {
    string: {
      type: "string",
      example: "Unable to Modulate {{component.id.name}}",
      description: "",
      allowNull: true,
      required: false,
    },
    stringTemplateReplacementMap: {
      type: "json",
      required: true,
      description: "",
    },
  },

  exits: {
    success: {
      description: "successfully formatted the string",
    },
  },

  fn: function (inputs, exits) {
    const { string, stringTemplateReplacementMap } = inputs;

    if (!string || string.trim() === '') {
      return exits.success(string || '');
    }

    let formattedString = string;
    const { paramMap: replacementTableMap } = stringTemplateReplacementMap
    try {
      const ALLOWED_TABLES = ['component', 'site'];
      const substringsToReplace = string.match(/{{(.*?)}}/g)?.map(match => match.slice(2, -2)) || [];
      for (let subStr of substringsToReplace) {
        const [tableKey, primaryKeyPlaceholder, columnName] = subStr.split('.');
        if (ALLOWED_TABLES.includes(tableKey)) {
          let specificTableData = replacementTableMap?.[tableKey]
          const primaryKeyValue = specificTableData?.[primaryKeyPlaceholder];
          if (!primaryKeyValue) continue;
          const formattedStr = `{{${tableKey}.${primaryKeyValue}.${columnName}}}`;
          formattedString = formattedString.replace(`{{${subStr}}}`, `${formattedStr}`);
        }
      }
      return exits.success(formattedString);
    } catch (error) {
      return exits.success(formattedString);
    }
  },
};
