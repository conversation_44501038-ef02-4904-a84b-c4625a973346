const moment = require("moment-timezone");
const alertSubscriptionService = require("../../services/alerts/alertSubscription/alertSubscription.public");
const alertIncidentHistoryService = require("../../services/alerts/alertIncidentHistory/alertIncidentHistory.public");

module.exports = {
  friendlyName: "getAlertDetailsByAlertId",
  description: "Fetches detailed information for a specific alert by alertId",
  example: [],
  inputs: {
    alertId: {
      type: "string",
      required: true,
      description: "ID of the alert in alert_inventory",
    },
    siteId: {
      type: "string",
      required: true,
      description: "SiteId of the alert for which the details are being fetched",
    },
  },
  exits: {
    success: {
      statusCode: 200,
      description: "Alert details fetched successfully",
    },
    notFound: {
      statusCode: 404,
      description: "Alert not found",
    },
    serverError: {
      statusCode: 500,
      description: "Server error",
    },
  },
  fn: async function (inputs, exits) {
    try {
      const { alertId, siteId } = inputs;

      const query = `
        SELECT
          alert_inventory.id AS "alertId",
          alert_inventory.asset_id AS "assetId",
          alert_inventory.asset_type AS "assetType",
          alert_inventory.name,
          alert_inventory.description,
          alert_inventory.severity,
          alert_inventory.created_by AS "createdBy",
          alert_inventory."createdAt",
          alert_inventory."updatedAt",
          alert_template.id AS "alertTemplateId",
          alert_template.alert_category AS "observerSource",
          alert_template.observer_execution_ref_id,
          CAST(alert_template.misc AS TEXT)::json AS "misc",
          alert_template.expression
        FROM alert_inventory
        JOIN alert_template ON alert_inventory.alert_template_ref_id = alert_template.id
        WHERE alert_inventory.id = $1
        AND alert_inventory.siteid = $2
        AND alert_template.status = 1
      `;

      const alertResult = await sails
        .getDatastore(process.env.SMART_ALERT_DB_NAME)
        .sendNativeQuery(query, [alertId, siteId]);
      const alertDetails = alertResult.rows[0];

      if (!alertDetails) {
        return exits.notFound({ message: "Alert not found" });
      }
      const stringTemplateReplacementMap = {
        paramMap: {
          component: {
            id: alertDetails.assetId,
          },
          site: {
            id: siteId,
          },
        },
      };
      if (alertDetails.expression && alertDetails.expression.trim() !== "") {
        alertDetails.expression = sails.helpers.globalParamFormatter.with({
          string: alertDetails.expression,
          stringTemplateReplacementMap,
        });
      }
      if (alertDetails.observerSource !== "recipe") {
        alertDetails.observationFormula = alertDetails.expression;
      }

      alertDetails.createdAt = moment(alertDetails.createdAt).tz("UTC").toISOString();
      alertDetails.updatedAt = moment(alertDetails.updatedAt).tz("UTC").toISOString();
      if (!alertDetails.misc.hasOwnProperty("formula")) {
        alertDetails.misc.formula = alertDetails.expression;
      }

      alertDetails.name =
        alertDetails.name && alertDetails.name.trim() !== ""
          ? sails.helpers.globalParamFormatter.with({
              string: alertDetails.name,
              stringTemplateReplacementMap,
            })
          : alertDetails.name || "";
      alertDetails.description =
        alertDetails.description && alertDetails.description.trim() !== ""
          ? sails.helpers.globalParamFormatter.with({
              string: alertDetails.description,
              stringTemplateReplacementMap,
            })
          : alertDetails.description || "";
      const subscribers = await alertSubscriptionService.getSubscribersByAlertId(alertId);
      alertDetails.subscribers = subscribers.map((sub) => sub.subscriberId);

      const alertState = await alertIncidentHistoryService.getAlertCurrentState(alertId);
      // If recipe-based alert, inject recipe metadata
      if (alertDetails.observerSource === "recipe" && alertState.blockId) {
        const recipeMeta = await alertIncidentHistoryService.getRecipeDetailsByBlockId(
          alertState.blockId
        );
        if (recipeMeta) {
          // override misc fields
          alertDetails.misc.formula = recipeMeta.formula;
          alertDetails.misc.componentsType = recipeMeta.componentsType;
          alertDetails.misc.recipelabel = recipeMeta.recipelabel;
          alertDetails.misc.runInterval = String(recipeMeta.runInterval);
          alertDetails.misc.runOn = String(recipeMeta.runOn);
          alertDetails.misc.schedules = recipeMeta.schedules;
          alertDetails.misc.expressionTemplate = recipeMeta.expressionTemplate;
          alertDetails.misc.operators = recipeMeta.operators;
          alertDetails.misc.params = recipeMeta.params;
          alertDetails.misc.recipeInfoId = recipeMeta.recipeInfoId;
          // update expression
          alertDetails.expression = recipeMeta.formula;
          // override description with child recipe description (like message-payload-builder does)
          alertDetails.description = recipeMeta.description || recipeMeta.parentDescription;
          // optionally override name with parent title if needed
          if (recipeMeta.parentTitle) {
            alertDetails.name = recipeMeta.parentTitle;
          }
        }
      }

      alertDetails.isAlertActive = alertState.issueResolvedAt === null ? 1 : 0;

      const mostRecentTimestamp = alertState.recentOccurredEventTs || alertState.issueOccurredAt;
      alertDetails.occurredAt = mostRecentTimestamp
        ? moment(mostRecentTimestamp).tz("UTC").toISOString()
        : null;

      alertDetails.incidentId = alertState.incidentId;
      alertDetails.occurredEventCount = alertState.occurredEventCount;
      return exits.success(alertDetails);
    } catch (error) {
      sails.log.error("[alerts > get-alert-details] Error fetching alert details");
      switch (error.code) {
        case "E_NOT_FOUND":
          return exits.notFound({ error: error.message });
        default:
          return exits.serverError(error);
      }
    }
  },
};
