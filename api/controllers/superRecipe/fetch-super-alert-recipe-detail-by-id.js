const SuperRecipeService = require('../../services/superRecipe/recipe.service');

module.exports = {
  friendlyName: 'Fetch Super Alert Recipe List of a site',
  description: 'Fetch all configured alert recipes for a site',

  inputs: {
    siteId: {
      type: 'string',
      required: true,
    },
    id: {
      type: 'number',
      required: true,
    }
  },

  exits: {
    success: {
      description: 'Successfully retrieved alerts'
    },
    serverError: {
      statusCode: 500,
      description: 'Internal server error',
      responseType: 'serverError'
    },
    notFound: {
      statusCode: 404,
      description: 'super alert recipe does not exist',
    }
  },

  fn: async function (inputs, exits) {
    const { siteId, id } = inputs;
    try {
      const result = await SuperRecipeService.fetchSuperAlertRecipeDetailById(siteId, id);
      if (!result) return exits.notFound({ message: "super alert recipe does not exist" });
      return exits.success(result);
    } catch (error) {
      sails.log.error('[list-configured-alerts] Error:', error);

      if (error.code === 'E_BAD_REQUEST') {
        return exits.badRequest({
          status: 'error',
          message: error.message
        });
      }

      return exits.serverError({
        status: 'error',
        message: 'Failed to list configured alerts',
        error: error.message
      });
    }
  }
};
