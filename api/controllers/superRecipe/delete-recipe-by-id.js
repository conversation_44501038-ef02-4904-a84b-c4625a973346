const recipeService = require('../../services/superRecipe/recipe.public');
const scheduleService = require('../../services/superRecipe/schedule/schedule.public');
const RecipeIotCommunicationInterface = require('../../services/superRecipe/lib/recipe.iot.communication.interface');
const auditEventLogService = require("../../services/auditEventLog/auditEventLog.public");
const { deleteRecipeFromSmartAlert } = require("../../services/smartAlert/smartAlert.public");
const {
  syncAlertTemplateStatus
} = require('../../services/smartAlert/smartAlertStatusSync.public');

module.exports = {
  friendlyName: 'delete-recipe-by-id',
  description: 'Deletes recipe by id',
  inputs: {
    _userMeta: {
      type: "json",
      required: true,
      example: { id: "<EMAIL>", _role: "admin", _site: "ash-tri" },
    },
    siteId: {
      type: 'string',
      required: true,
      example: 'gob-coi',
    },
    id: {
      type: 'string',
      required: true,
      example: '123',
    },
    type: {
      type: 'string',
      required: true,
      example: 'action/alert',
    }
  },
  exits: {
    serverError: {
      responseType: 'serverError',
      description: '[superRecipe -> delete-recipe-by-id] Server Error!',
    },
    badRequest: {
      statusCode: 400,
      responseType: 'badRequest',
      description: '[superRecipe -> delete-recipe-by-id] Bad Request!',
    },
    forbidden: {
      statusCode: 403,
      responseType: 'forbidden',
      description: '[superRecipe -> delete-recipe-by-id] forbidden Request!',
    },
    notFound: {
      statusCode: 404,
      description: '[superRecipe -> delete-recipe-by-id] Not Found',
    },
    success: {
      statusCode: 200,
      description: '[superRecipe -> delete-recipe-by-id] Recipe Deleted successfully',
    },
  },
  fn: async function(inputs, exits)  {
    try {
      const {
        id,
        siteId,
        type,
        _userMeta: { id: userId },
      } = inputs;

      const transactionId = this.req?.headers?.["x-transaction-id"];

      if (!id || !siteId) {
        return exits.badRequest({ message: 'id/siteId is required' });
      }
      if (!['action', 'alert'].includes(type)) {
        return exits.badRequest({ message: 'Type of recipe can be action or alert only' });
      }

      const recipeData = await recipeService.getActionRecipeInfoById(id, type, siteId);
      if (recipeData?.error) {
        return exits.badRequest({ message: 'Recipe not found' });
      }

      const auditPayload = {
        event_name: "state_delete_super_recipe",
        user_id: userId,
        site_id: siteId,
        asset_id: "recipe_" + id,
        req: this.req,
        prev_state: recipeData,
        curr_state: null,
      };

      auditEventLogService.emit(auditPayload);

      if (recipeData?.is_deployed === 1) {
        /**Recipe is deployed, so only trigger removal from IOT*/
        await RecipeIotCommunicationInterface.deleteSuperRecipeFromController({
          siteId,
          runOn: recipeData.run_on,
          recipeId: recipeData.rid,
          transactionId,
        });

        return exits.success({
          message: `Recipe is deployed. Request to remove from IOT controller has been initiated.`,
          runOn: recipeData.run_on
        });
      }

      if (recipeData?.recipe_type === "alert") {
        try {
          await recipeService.deleteSmartAlertRecipeById(siteId,id);
          sails.log.info("[Delete-Recipe-From-Smart-Alert] Alert Recipe Deleted.");
        } catch (e) {
          sails.log.error("[Delete-Recipe-From-Smart-Alert]");
          sails.log.error(e);
        }
      }

      // Sync smart alert status for super recipe deletion
      try {
        await syncAlertTemplateStatus(recipeData.rid, 'delete', siteId);
      } catch (error) {
        sails.log.error(`[SuperRecipe] Failed to sync smart alert status for recipe deletion: ${recipeData.rid}`, error);
      }

      await Promise.all([
        recipeService.delete(id),
        recipeService.delete_children_recipes({ parent_recipe_id: id }),
        scheduleService.delete({ recipe_id: id })
      ]);

      return exits.success({
        message: `Recipe with id=${id} deleted successfully.`,
        runOn: recipeData.run_on
      });
    } catch (error) {
      sails.log.error('[superRecipe -> delete-recipe-by-id]', error);
      if (error.code === 'E_NOT_FOUND') {
        return exits.badRequest({ message: error.message });
      }
      return exits.serverError(error);
    }
  }
};
