const recipeService = require('../../services/superRecipe/recipe.public');
const RecipeIotCommunicationInterface = require('../../services/superRecipe/lib/recipe.iot.communication.interface');
const auditEventLogService = require("../../services/auditEventLog/auditEventLog.public");
const {
  syncAlertTemplateStatus
} = require('../../services/smartAlert/smartAlertStatusSync.public');

module.exports = {
  friendlyName: 'manage-running-state',
  description: 'Manage Running State',
  inputs: {
    _userMeta: {
      type: "json",
      required: true,
      example: { id: "<EMAIL>", _role: "admin", _site: "ash-tri" },
    },
    siteId: {
      type: 'string',
      required: true,
      example: 'gob-coi',
    },
    id: {
      type: 'string',
      required: true,
      example: '123',
    },
    status: {
      type: 'number',
      required: true,
      example: 1,
    },
    recipeType: {
      type: 'string',
      required: true,
      example: 'action',
    }
  },
  exits: {
    serverError: {
      responseType: 'serverError',
      description: '[superRecipe -> manage-running-state] Server Error!',
    },
    badRequest: {
      statusCode: 400,
      responseType: 'badRequest',
      description: '[superRecipe -> manage-running-state] Bad Request!',
    },
    forbidden: {
      statusCode: 403,
      responseType: 'forbidden',
      description: '[superRecipe -> manage-running-state] forbidden Request!',
    },
    notFound: {
      statusCode: 404,
      description: '[superRecipe -> manage-running-state] Not Found',
    },
    entityUnprocessable: {
      statusCode: 422
    },
    success: {
      statusCode: 200,
      description: '[superRecipe -> manage-running-state] Request Registered Successfully',
    },
  },
  fn: async function(inputs, exits)  {
    try {
      const {
        id,
        siteId,
        status,
        recipeType,
        _userMeta: { id: userId },
      } = inputs;

      const transactionId = this.req?.headers?.["x-transaction-id"];

      if (!id || !siteId || ![0, 1].includes(status)) {
        return exits.badRequest({ message: 'id/siteId is required' });
      }

      if (!['action', 'alert'].includes(recipeType)) {
        return exits.badRequest({ message: 'Type of recipe can be action or alert only' });
      }

      const recipeData = await recipeService.getActionRecipeInfoById(id, recipeType, siteId);

      if (+recipeData?.['is_deployed'] !== 1) {
        return exits.badRequest({ problems: ['Recipe is not deployed'] });
      }
      await RecipeIotCommunicationInterface.updateRunningState({
        siteId,
        runOn: recipeData.run_on,
        status,
        recipeId: recipeData?.rid,
        transactionId,
      });

      // Sync smart alert status for super recipe play/pause
      try {
        const lifecycleEvent = status === 1 ? 'pause' : 'play';
        await syncAlertTemplateStatus(recipeData.rid, lifecycleEvent, siteId);
      } catch (error) {
        sails.log.error(`[SuperRecipe] Failed to sync smart alert status for recipe ${status === 1 ? 'pause' : 'play'}: ${recipeData.rid}`, error);
      }

      const auditPayload = {
        event_name: "state_running_super_recipe",
        user_id: userId,
        site_id: siteId,
        asset_id: "recipe_" + id,
        req: this.req,
        prev_state: { switch_off: recipeData?.switch_off },
        curr_state: { switch_off: status },
      };

      auditEventLogService.emit(auditPayload);

      return exits.success(recipeData);
    } catch (error) {
      sails.log.error('[superRecipe -> manage-running-state]', error);
      if (error?.code?.includes('[AWS-IOT-CORE-SERVICE')) {
        return exits.entityUnprocessable({
          message: error?.message,
          code: error?.code
        });
      }
      return exits.serverError(error);
    }
  }
};
