const recipeService = require('../../services/superRecipe/recipe.public');
const { validateRecipeInfo } = require('../../utils/super_recipe/create-recipe.util');
const RecipeIotCommunicationInterface = require('../../services/superRecipe/lib/recipe.iot.communication.interface');
const RecipeIotPayloadBuilder = require('../../services/superRecipe/lib/recipe.iot.payload.builder');

module.exports = {
  friendlyName: 'Edit recipe',
  description: 'Edit a new recipe',
  inputs: {
    _userMeta: {
      type: 'json',
      required: true,
      example: {
        id: 'userId',
        _role: 'role',
        _site: 'siteId',
      },
      description: 'User meta information added by default to authenticated routes',
    },
    siteId: {
      type: 'string',
      required: true,
      example: 'gob-coi',
    },
    id: {
      type: 'string',
      required: true,
      example: '123',
    },
    recipeInfo: {
      type: 'json',
      required: true,
      description: 'Payload containing recipe data for creation',
    },
  },
  exits: {
    serverError: {
      responseType: 'serverError',
      description: '[superRecipe -> edit-recipe-by-id] Server Error!',
    },
    badRequest: {
      statusCode: 400,
      responseType: 'badRequest',
      description: '[superRecipe -> edit-recipe-by-id] Bad Request!',
    },
    forbidden: {
      statusCode: 403,
      responseType: 'forbidden',
      description: '[superRecipe -> edit-recipe-by-id] forbidden Request!',
    },
    notFound: {
      statusCode: 404,
      description: '[superRecipe -> edit-recipe-by-id] Not Found',
    },
    success: {
      statusCode: 200,
      description: '[superRecipe -> edit-recipe-by-id] Recipe Updated Successfully',
    },
  },
  async fn(inputs, exits) {
    try {
      const {
        siteId,
        recipeInfo,
        id,
        _userMeta: { id: userId },
      } = inputs;

      const transactionId = this.req?.headers?.["x-transaction-id"];

      if (!id || !siteId) {
        return exits.badRequest({ message: 'id/siteId is required' });
      }

      const recipeInfoPayload = {
        ...recipeInfo,
        site_id: siteId
      };

      validateRecipeInfo(recipeInfoPayload);
      const recipeInfoData = await recipeService.createRecipePayload(recipeInfoPayload, userId);
      if (_.isEmpty(recipeInfoData)) {
        return exits.badRequest({ message: 'Invalid recipe payload' });
      }

      const recipeDetail = await recipeService.getActionRecipeInfoById(id, recipeInfoData.recipe_type, siteId);

      if (recipeDetail?.error) {
        return exits.badRequest({ message: 'Recipe not found' });
      }

      if (recipeDetail?.is_deployed === 1) {
        /**Recipe is deployed, so only trigger removal from IOT*/
        await RecipeIotCommunicationInterface.updateSuperRecipeFromController(
          siteId,
          recipeDetail.run_on,
          {
            ...recipeInfoData,
            rid: recipeDetail?.rid,
            transactionId
          },
        );

        return exits.success({
          message: `Recipe is deployed. Request to remove from IOT controller has been initiated.`,
          runOn: recipeDetail.run_on
        });
      }

      const newRecipe = await recipeService.update({ id }, recipeInfoData);
      try {
        if (recipeDetail.recipe_type === "alert") {
          await recipeService.syncSmartAlertRecipeById(siteId, id);
        }
      } catch (e) {
        sails.log.error(e);
      }
      return exits.success({
        message: 'Successfully updated recipe',
        recipeInfo: newRecipe,
      });
    } catch (error) {
      sails.log.error('[superRecipe -> edit-recipe-by-id]', error);
      switch (error.code) {
        case 'E_INPUT_VALIDATION':
          return exits.badRequest({ err: error.message });
        case 'E_NOT_FOUND':
          return exits.notFound({ err: error.message });
        default:
          return exits.serverError(error);
      }
    }
  },
};
