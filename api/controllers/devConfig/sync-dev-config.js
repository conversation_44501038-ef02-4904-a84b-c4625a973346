const devConfigService = require('../../services/devConfig/devConfig.service');
const iotCoreService = require('../../services/iot/iot.service')
const auditEventLogService = require('../../services/auditEventLog/auditEventLog.public');

module.exports = {
  friendlyName: 'Sync device config',
  description: 'Generate the config for a controller',
  curl: `curl --location --request POST 'localhost:1337/m2/site/gob-coi/controller/18918/sync-dev-config`,
  inputs: {
    siteId: {
      type: 'string',
    },
    _userMeta: {
      type: 'ref',
      required: true,
      description: 'User meta data',
    }
  },
  exits: {
    badRequest: {
      responseType: 'badRequest',
      description: '[sync-device-config] Bad Request!',
    },
    serverError: {
      responseType: 'serverError',
      description: '[sync-device-config] Server Error!',
    },
     unprocessableEntity: {
      statusCode: 422,
      description: '[sync-device-config] Unprocessable Entity!'
    },
    notFound: {
      statusCode: 404,
      description: '[sync-device-config] Not Found!',
    },
    success: {
      responseType: 'ok',
      description: '[sync-device-config] Success!',
    }
  },
  fn: async function ({siteId, _userMeta}, exits) {
    try {
      if (!siteId.trim()) {
        return exits.badRequest({
          err: 'Site ID is required'
        });
      }


      const {id: userId} = _userMeta;
   
      const controllerIds = await iotCoreService.getSiteUnsyncedControllers(siteId);
      if (_.isEmpty(controllerIds)) return exits.success([]);

      const syncPromises = controllerIds.map(controllerId => 
        devConfigService.sendDeviceConfigSyncRequest({siteId, controllerId, userId})
      );

      const results = await Promise.allSettled(syncPromises);
      const auditPayload = {
          event_name: "sync_dev_config",
          user_id: userId,
          site_id: siteId,
          req: this.req,
          prev_state: null,
          curr_state: {
            controllerIds: JSON.stringify(controllerIds),
          },
      };
        
      auditEventLogService.emit(auditPayload);
      const response = results.map((result, index) => {
        const resp = {
          controllerId: controllerIds[index],
          status: result.status
        }

        if (result.status === 'rejected') {
           resp.error = result.reason.message 
        }

        if (result.status === 'fulfilled') {
          resp.message = result.value.msg;
          resp.syncDetails = result.value.syncDetails;
        }

        return resp;
      });

      return exits.success(response);

    } catch (e) {
      if (e.HTTP_STATUS_CODE == 400) {
          sails.log('Error > bad-request [sync-device-config]')
          return exits.badRequest({err: e.message, data: e.data});
      } else if (e.HTTP_STATUS_CODE == 422) {
        sails.log('Error > unprocessed-entity [sync-device-config]')
        return exits.unprocessableEntity({
          err: e.message,
          data: e.data
        })
      } else if (e.HTTP_STATUS_CODE == 404) {
        sails.log('Error > not-found [sync-device-config]')
        return exits.notFound({
          err: e.message,
          data: e.data
        })
      } else  {
          sails.log.error('error > sync-device-config',e);
          return exits.serverError(e);
      }
    }
  }
};