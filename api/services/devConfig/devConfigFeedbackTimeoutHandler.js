const os = require('os');
const RedisNotification = require("../RedisNotification");
const { notifyJouleTrackPublicRoom } = require("../socket/socket.public");
const RedisClient = require('../cache/cache.public');
const devConfigConstants = require("../../utils/devConfig/Constants");


function isExpiredCacheIsDevConfig(key) {
  const regex = /^devConfig:site:[a-zA-Z0-9_-]+:controller:[a-zA-Z0-9_-]+:requestId:[a-zA-Z0-9_-]+$/;
  return regex.test(key);
}

(async () => {
  RedisNotification.on("message", async (pattern, channel, expiredKey) => {
    try {
      if (!isExpiredCacheIsDevConfig(expiredKey)) return;
      const isLockAcquired = await RedisClient.acquireLock(`timeout:event:lock:${expiredKey}`, os.hostname(), 3);
      if (!isLockAcquired) {
        sails.log.error(`Message is already processing by other server`);
        return;
      }

      const siteId = expiredKey.split(':')[2];
      const controllerId = expiredKey.split(':')[4];
      const requestId = expiredKey.split(':')[6];
      if (!siteId || !controllerId) {
        sails.log.error(`Invalid expired key format: ${expiredKey}`);
        return;
      }
      sails.log.info(`[devConfigTimeOut] Handling timeout event for siteId: ${siteId}, controllerId: ${controllerId} and requestId: ${requestId}`);
      await DevConfig.updateOne(
        { siteId, controllerId , status: devConfigConstants.DEV_CONFIG_STATUS.IN_PROGRESS },
        {
          status: devConfigConstants.DEV_CONFIG_STATUS.FAILED,
        }
      );
      notifyJouleTrackPublicRoom(siteId, 'devConfig', {
        data: {
          siteId,
          controllerId,
          status: 1,
        },
        event: "controllerSyncTimeout"
      });
      sails.log.error({
         data: {
          siteId,
          controllerId,
          status: 1,
        },
        event: "devConfigControllerSyncTimeout",
      })
      await RedisClient.releaseLock(`timeout:event:lock:${expiredKey}`, os.hostname());
    } catch (e) {
      sails.log.error('DevConfig>Timeout Event Handling');
      sails.log.error(e.message || e);
    }
  });
})();
