const errorUtils = require('../../utils/devConfig/ErrorValidation');
const iotCoreService = require('../iotCore/iotCore.service');
const devConfigService = require('../devConfig/devConfig.private');
const devConfigUtils = require('../../utils/devConfig/Index');
const parameterService = require('../parameter/parameter.public');
const dynamokeystoreservice = require('../dynamokeystore/dynamokeystore.public');
const componentService = require('../component/component.public');
const devConfigConstants = require('../../utils/devConfig/Constants');
const redisService = require('../cache/cache.public');
const iotCoreConstants = require("../../utils/iotCore/constants");
require('./devConfigFeedbackTimeoutHandler')


const DEV_CONFIG_STATUS = devConfigConstants.DEV_CONFIG_STATUS;
const CONTROLLER_TYPES = devConfigConstants.CONTROLLER_TYPES;
const uuid = require('uuid');
const devConfigInputValidation = require('../../utils/devConfig/InputValidation');
const deviceservice = require('../device/device.public');
const { notifyJouleTrackPublicRoom } = require('../socket/socket.public');

module.exports = {
  updateDevConfigFeedback,
  sendDeviceConfigSyncRequest,
  getDeviceConfig,
  executeDevConfigStateChange,
  fetchDevConfigBySiteId,
  updateOne: devConfigService.updateOne,
  setDevConfigCache,
  removeDevConfigCache
};


async function sendDeviceConfigSyncRequest({siteId, controllerId, userId, deviceId}) {
  const response = {
    msg: "Sync request sent successfully. Please wait for sometime",
    syncDetails: null,
  }
  const device = await deviceservice.findOne({ deviceId: controllerId, siteId });
  if (!device) errorUtils.throwExceptionDeviceNotFound(controllerId);

  const deviceType = device.deviceType;
  if (!CONTROLLER_TYPES.includes(deviceType)) errorUtils.throwExceptionInvalidController();

  const existingDevConfig = await devConfigService.findOne({
      controllerId,
      siteId,
  });

  if (!existingDevConfig) {
    const result = await publishSyncControllerReqToIoT({
      siteId,
      controllerId,
      userId,
      deviceId,
      deviceType
    });
    response.msg = "The sync request has been sent successfully. Kindly wait a moment.";
    response.syncDetails = result;
    return response;
  }

  response.syncDetails = existingDevConfig;
  if (existingDevConfig.status == DEV_CONFIG_STATUS.SUCCESS) {
    response.msg = "The controller has already been successfully synced.";
    return response
  }

   if (existingDevConfig.status == DEV_CONFIG_STATUS.IN_PROGRESS) {
    response.msg = "Sync request already in progress. Please wait for sometime";
    return response
  }
  // It means it is in QUEUE state
  const newDevConfigReq = await publishSyncControllerReqToIoT({
      siteId,
      controllerId,
      userId,
      deviceId,
      deviceType,
      lastSyncTime: existingDevConfig.lastSyncedAt
    });
  response.msg = "The sync request has been sent successfully. Kindly wait a moment.";
  response.syncDetails = newDevConfigReq
  return response;
}


const publishSyncControllerReqToIoT = async function(iotDataPacket) {
  const { siteId, controllerId, userId, deviceId, deviceType, lastSyncTime} = iotDataPacket;
  const topic = siteId + '/sync/' + controllerId + '/devconfig';
  const requestId = uuid.v4();
  const syncController = {
    "operation": "syncDevConfig",
    "requestId": requestId,
    "config": {
      "siteId": siteId,
      "controllerId": controllerId,
      "lastSyncTime": lastSyncTime
    }
  };
  sails.log('[publishSyncControllerReqToIoT > Sending data packet to IOT] at time:' + lastSyncTime, syncController, 'with requestId:', requestId);
  const isPublished = await iotCoreService.publish(topic, syncController);
  if (!isPublished) {
    errorUtils.throwExceptionUnableToPublishIOT(topic, syncController);
  }
  const storeDevConfigReq = await storeSyncRequest({
    status: DEV_CONFIG_STATUS.IN_PROGRESS,
    siteId: siteId,
    controllerId: controllerId,
    syncedBy: userId,
    deviceId: deviceId,
    deviceType
  });
  await setDevConfigCache(siteId, controllerId, requestId)
  return storeDevConfigReq;
}

async function updateDevConfigFeedback({
    siteId,
    controllerId,
    status,
    requestId
  }) {
  const response = {
    msg: "DevConfig updated successfully",
    devConfig: null,
  }
  const device = await deviceservice.findOne({ deviceId: controllerId, siteId });
  if (!device) errorUtils.throwExceptionDeviceNotFound(controllerId);

  const deviceType = device.deviceType;
  if (!CONTROLLER_TYPES.includes(deviceType)) errorUtils.throwExceptionInvalidController();

  const existingDevConfig = await devConfigService.findOne({siteId, controllerId});
  if (!existingDevConfig) {
    errorUtils.throwExceptionDevConfigNotFound(requestId);
  }

  let isSuccess = status == 1;
  let isExistingDevConfigStatusInProgress = existingDevConfig.status == DEV_CONFIG_STATUS.IN_PROGRESS;
  if (!isExistingDevConfigStatusInProgress) {
    sails.log.warn(`[updateDevConfigFeedback]DevConfig is not in the 'in-progress' state for controller: ${controllerId}`);
    errorUtils.throwExceptionInvalidDevConfigState(siteId, controllerId);
  }

  const updateDevConfigValues = {
    status: DEV_CONFIG_STATUS.FAILED,
  }

  if (isSuccess) {
    updateDevConfigValues.status = DEV_CONFIG_STATUS.SUCCESS
    updateDevConfigValues.lastSyncedAt = new Date();
  }

  const updatedDevConfig = await devConfigService.updateOne({
    id: existingDevConfig.id,
  }, updateDevConfigValues);
  response.devConfig = updatedDevConfig;
  sails.log.info(`[updateDevConfigFeedback] DevConfig updated for controller: ${controllerId} with requestId:${requestId} and status: ${isSuccess ? 'SUCCESS' : 'FAILED'}`);
  await removeDevConfigCache(siteId, controllerId, requestId);
  notifyJouleTrackPublicRoom(siteId, 'devConfig', {
    data: {
      siteId,
      controllerId,
      status: isSuccess ? iotCoreConstants.SYNC_STATUS.SYNCED: iotCoreConstants.SYNC_STATUS.FAILED,
    },
    event: "controllerSyncFeedback"
  });
  return response;
}

async function getDeviceConfig(siteId, cntrlId, isGlobal=true) {
  let devParamMap = {};
  let devFeedbackMap = {};
  let devConfig = {};

  const device = await deviceservice.findOne({ deviceId: cntrlId , siteId});
  if (!device) errorUtils.throwExceptionControllerNotConfigured(cntrlId);

  const { deviceType } = device;
  if (!CONTROLLER_TYPES.includes(deviceType)) errorUtils.throwExceptionNotAController();

  const results = await Promise.allSettled([
    parameterService.getParametersBySiteId(siteId),
    dynamokeystoreservice.findOne({ key: `${siteId}_configTS` }),
    Sites.findOne(siteId),
    deviceservice.find({siteId}),
    componentService.getComponentsBySiteId(siteId)
  ]);

  const rejection = results.find(r => r.status === 'rejected');
  if (rejection) {
    errorUtils.throwExceptionServerError(rejection.reason);
  }

  const [params, tsData, site, deviceArr, components] = results.map(res => res.value);

  components && components.forEach(component => devConfigUtils.appendFeedbackPort(component, devFeedbackMap));
  params.forEach(param => devConfigUtils.filterParameters(param, devParamMap, devFeedbackMap));

  if (site && site.hasOwnProperty('networks')) {
    site.networks = JSON.parse(site.networks);
  }
  if (site && site.hasOwnProperty('unitCost')) {
    site.unitCost = site.unitCost?.toString();
  }

  const slaveIds = [];

  deviceArr
  .forEach(device => {
    if ('isSlaveController' in device) {
      device.isSlaveController = device.isSlaveController == 1 || device.isSlaveController == true;
    }
    if (device.controllerId === cntrlId || device.deviceId === cntrlId) {
      if (devParamMap[device.deviceId]) device.param = devParamMap[device.deviceId];
      devConfig[device.deviceId] = device;
      if (device.hasOwnProperty("isSlaveController")) {
        slaveIds.push(device.deviceId);
      }
    }

  });

  slaveIds.forEach(slaveId => {
    deviceArr
      .filter(device => device.controllerId === slaveId || device.deviceId === slaveId)
      .forEach(device => {
        if (devParamMap[device.deviceId]) device.param = devParamMap[device.deviceId];
        devConfig[device.deviceId] = device;
      });
  });

  const timestamp = tsData?.value || Date.now();
  if (!tsData) await dynamokeystoreservice.updateConfigTs({ siteId });

  const response = {
     schema: {
      self: device,
      timestamp,
      site
    },
    devConfig
  }

  if (isGlobal) {
    response.schema.components = components;
    response.schema.devices = deviceArr;
  }
  return response
}


async function markParticularControllerOutOfSync(siteId, controllerId, userId) {
  devConfigInputValidation.validateSiteIdUserIdAndDeviceId({ siteId, deviceId:controllerId, userId});
  const controllerInfo = await deviceservice.findOne({ deviceId:controllerId, siteId });
  if (!controllerInfo) {
    sails.log.error("[Device >> markControllerOutOfSync] Controller not found", { siteId, controllerId });
    return;
  }
  const updatedDevConfig = await devConfigService.updateOne({
    siteId,
    controllerId
  },{
    status: DEV_CONFIG_STATUS.QUEUED,
    syncedBy: userId
  })
  return updatedDevConfig;
}

async function getControllerInfoByDeviceId(deviceId, siteId) {
   devConfigInputValidation.validateSiteAndDeviceId({ siteId, deviceId });
   const deviceInfo = await deviceservice.findOne(deviceId, siteId);
    if (!deviceInfo) {
      sails.log.error("[Device >> markControllerOutOfSync] Device not found", { siteId, deviceId });
      return;
    };
    const deviceType = deviceInfo.deviceType;
    if (CONTROLLER_TYPES.includes(deviceType.toLowerCase())) {
      return deviceInfo;
    }
    const controllerId = (await deviceservice.getDeviceControllerMap([deviceId]))[deviceId];
    if (!controllerId) {
      sails.log.error("[Device >> markControllerOutOfSync] Controller ID not found for device", { siteId, deviceId });
      return;
    }
    const controllerInfo =  await deviceservice.findOne({deviceId: controllerId, siteId});
   return controllerInfo;
}

/**
 * Initializes device configuration for a controller
 * @param {Object} params - Configuration parameters
 * @param {string} params.siteId - The ID of the site
 * @param {string} params.controllerId - The ID of the controller
 * @param {string} params.deviceType - The type of the device (e.g., 'jouleBox')
 * @param {string} params.userId - The ID of the user initiating the configuration
 * @param {string} params.deviceId - The ID of the device
 * @returns {Promise<void>}
 */
async function createControllerDevConfigInQueueState({siteId, controllerId, deviceType, userId, deviceId}) {
    // Create new entry with QUEUED status
    const requestId = uuid.v4();
    await devConfigService.create({
      requestId,
      siteId,
      controllerId,
      status: DEV_CONFIG_STATUS.QUEUED,
      synced_by: userId,
      deviceId,
      controllerType: deviceType
    });
}

/**
 * Marks all controllers in a site as out of sync
 * @param {string} siteId - The ID of the site
 * @returns {Promise<void>}
 */
async function markSiteAllControllersOutOfSync(siteId, userId) {
  devConfigInputValidation.validateSiteIdAndUserId({ siteId, userId });
  const updatedDevConfig = await devConfigService.update({
    siteId
  }, {
    status: DEV_CONFIG_STATUS.QUEUED,
    syncedBy: userId
  });
  return updatedDevConfig
}

async function markSiteJouleBoxOutOfSync(siteId, userId) {
   devConfigInputValidation.validateSiteIdAndUserId({ siteId, userId });
    const controllerIds = await deviceservice.fetchJouleBoxControllerIdsBySiteId(siteId)
    if (!controllerIds || controllerIds.length === 0) {
      sails.log.warn("[Device >> markSiteJouleBoxOutOfSync] No JouleBox controllers found for site", { siteId });
      return;
    }
    const updatedDevConfig = await devConfigService.update({
      siteId,
      controllerId: {
        in: controllerIds
      }
    }, {
      status: DEV_CONFIG_STATUS.QUEUED,
      syncedBy: userId
    });
    return updatedDevConfig;
}

async function storeSyncRequest({ siteId, controllerId, status, syncedBy, deviceId, deviceType }) {
  try {
    const existingRequest = await devConfigService.findOne({siteId, controllerId });
    if (existingRequest) {
      return await devConfigService.updateOne({ id: existingRequest.id }, { status, syncedBy });
    } else {
      return await devConfigService.create({
        siteId,
        controllerId,
        status,
        syncedBy,
        deviceId,
        controllerType: deviceType
      });
    }
  } catch (error) {
    sails.log.error("[Device >> storeSyncRequest]", { siteId, controllerId, error });
    throw error;
  }
}

/**
 * Handles different device configuration operations based on the operation type
 * @param {Object} payload - The payload containing operation details
 * @param {string} payload.siteId - The ID of the site
 * @param {string} payload.deviceId - The ID of the device
 * @param {string} payload.userId - The ID of the user performing the operation
 * @param {string} isSiteConfig - Indicates if the operation is for site configuration
 * @param {boolean} isNewController - Indicates if the operation is for a new controller
 * @param {boolean} isComponentConfig - Indicates if the operation is for a component configuration
 * @param {boolean} isControllerConfigChange - Indicates if the operation is for a controller configuration
 * @returns {Promise<void>}
 */
async function executeDevConfigStateChange({
  siteId,
  deviceId,
  userId,
  isSiteConfig = false,
  isNewController = false,
  isComponentConfig = false,
  isControllerConfigChange = false
}) {
  try {
    devConfigInputValidation.validateDevConfigStateChangeInputs({
      siteId,
      deviceId,
      userId
    });

    if (isSiteConfig) {
      return markSiteAllControllersOutOfSync(siteId, userId);
    }

    if (isComponentConfig || isControllerConfigChange) {
      return markSiteJouleBoxOutOfSync(siteId, userId);
    }

    if (!deviceId) {
      sails.log.error("[Device >> executeDevConfigStateChange] Device not found for site", { siteId });
      return;
    }


    let controllerId, controllerDeviceType;
    const controllerInfo = await getControllerInfoByDeviceId(deviceId, siteId);
    if (!controllerInfo) {
      sails.log.error("[Device >> executeDevConfigStateChange] Controller not found for device", { siteId, deviceId });
      return;
    }
    controllerId = controllerInfo.deviceId;
    controllerDeviceType = controllerInfo.deviceType;

    if (isNewController && controllerDeviceType) {
      const isJouleBox = controllerDeviceType.toLowerCase() === 'joulebox';

      await createControllerDevConfigInQueueState({
        siteId,
        controllerId,
        deviceType: controllerDeviceType,
        userId,
        deviceId
      });

      if (!isJouleBox) {
        await markSiteJouleBoxOutOfSync(siteId, userId);
      }
    }


    if (controllerDeviceType && controllerDeviceType.toLowerCase() === 'joulebox') {
      return await markParticularControllerOutOfSync(siteId, controllerId, userId);
    } else {
      await markParticularControllerOutOfSync(siteId, controllerId, userId);
      return await markSiteJouleBoxOutOfSync(siteId, userId);
    }

  } catch (e) {
    sails.log.error("[Device >> executeDevConfigStateChange]", {
      siteId,
      deviceId,
      userId,
      isSiteConfig,
      isNewController,
      isComponentConfig,
      error: e
    });
  }
}

async function fetchDevConfigBySiteId(siteId) {
  const devConfigs = await devConfigService.find({
    where: {
      siteId,
    },
    // sort: "updatedAt DESC",
  });

  return devConfigs;
}

/**
 * Sets a temporary cache key for DevConfig with a 20-second TTL.
 * @param {string} siteId - Site identifier
 * @param {string} controllerId - Controller identifier
 */
async function setDevConfigCache(siteId, controllerId, requestId) {
  const cacheKey = generateDevConfigCacheKey(siteId, controllerId, requestId);
  const cacheTTL = 2 * 60; // 2 min

  try {
    await redisService.set(cacheKey, true, 'EX', cacheTTL);
    sails.log.debug(`[setDevConfigCache] Cache set for ${cacheKey} with TTL ${cacheTTL}s`);
  } catch (error) {
    sails.log.error("[setDevConfigCache] Failed to set cache", {
      siteId,
      controllerId,
      error: error.message || error,
    });
  }
}

async function removeDevConfigCache(siteId, controllerId, requestId) {
  const cacheKey = generateDevConfigCacheKey(siteId, controllerId, requestId);
  try {
    await redisService.delete(cacheKey);
    sails.log.debug(`[removeDevConfigCache] Cache removed for ${cacheKey}`);
  } catch (error) {
    sails.log.error("[removeDevConfigCache] Failed to remove cache", {
      siteId,
      controllerId,
      error: error.message || error,
    });
  }
}

function generateDevConfigCacheKey(siteId, controllerId, requestId) {
  return `devConfig:site:${siteId}:controller:${controllerId}:requestId:${requestId}`;
}
