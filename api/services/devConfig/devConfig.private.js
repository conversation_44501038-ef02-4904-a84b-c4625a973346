
module.exports = {

  async findOne(criteria) {
    return DevConfig.findOne(criteria);
  },

  async find(criteria) {
    return DevConfig.find(criteria);
  },

  async update(criteria, values) {
    return DevConfig.update(criteria).set(values).fetch();
  },

  async create(values) {
    return DevConfig.create(values).fetch();
  },

  async updateOne(criteria, values) {
    return DevConfig.updateOne(criteria).set(values);
  },
}