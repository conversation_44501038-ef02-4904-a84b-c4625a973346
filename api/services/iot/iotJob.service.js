const constants = require("../../utils/iotCore/constants");
const { v4: uuidv4 } = require("uuid");
const {
  IoTClient,
  GetPackageVersionCommand,
  CreateJobCommand,
  ListJobExecutionsForJobCommand,
} = require("@aws-sdk/client-iot");
const softwareJobMappingService = require("./softwareJobMapping.service");
const controllerUpdateStatusService = require("./controllerUpdateStatus.service");
const softwarePackageUpdateRequestService = require("./softwarePackageUpdateRequest.service");

// Initialize the IoT Client
const iotClient = new IoTClient({ region: process.env.IOT_CORE_REGION || "us-west-2" });

async function deployJob(
  targetList,
  jobDocument,
  jobId,
  jobDescription,
  destinationPackageVersions
) {
  const params = {
    jobId,
    targets: targetList,
    document: jobDocument,
    description: jobDescription,
    presignedUrlConfig: constants.JOB_DEPLOY_CONFIG.presignedUrlConfig,
    destinationPackageVersions: destinationPackageVersions,
  };

  try {
    const command = new CreateJobCommand(params);
    const response = await iotClient.send(command);
    return response.jobId;
  } catch (error) {
    console.error("Error creating IoT job:", error);
    throw error;
  }
}

/**
 * Fetches the recipe (job document) from a given package name and version.
 * @param {string} packageName - The name of the package.
 * @param {string} versionName - The version of the package.
 * @returns {Promise<Object>} - The recipe object.
 */
async function getJobDocumentFromPackage(packageName, versionName) {
  try {
    const command = new GetPackageVersionCommand({
      packageName,
      versionName,
    });

    const response = await iotClient.send(command);

    if (!response.recipe) {
      throw new Error("Recipe not found in package version response.");
    }

    const jobDocument = JSON.parse(response.recipe);

    return jobDocument;
  } catch (error) {
    console.error("Failed to fetch package version recipe:", error);
    throw error;
  }
}

async function updateDatabaseWithJobId(controllers, jobId, updateVersion, userId, siteId) {
  // Add requests to software_package_update_request table
  const requestId = await softwarePackageUpdateRequestService.create({
    userGeneratedJobId: jobId,
    createdBy: userId,
  });

  const record = {
    version: updateVersion,
    userJobIdRef: requestId.id,
    awsJobId: jobId,
    siteId: siteId,
  };

  // 1. Insert into software_job_mappings
  const jobMapping = await softwareJobMappingService.create(record);

  // Create array of controller update status records
  const controllerUpdates = controllers.map((controller) => ({
    softwareJobMappingId: jobMapping.id,
    controllerId: parseInt(controller.id.replace("controller-", "")),
    status: constants.CONTROLLER_UPDATE_STATUS.QUEUE,
    siteId: siteId,
    currentVersion: controller.currentSoftwareVersion,
    expectedVersion: updateVersion,
    controllerType: controller.controllerType,
  }));

  // Bulk create all records in a single query
  await controllerUpdateStatusService.createEach(controllerUpdates);
}

/**
 * Update the status of jobs in the database
 * @returns {Promise<void>}
 */
async function updateActiveJobStatus() {
  const activeJobs = await softwareJobMappingService.fetchActiveJobs();

  // Process jobs in parallel with Promise.all
  await Promise.all(
    activeJobs.map(async (job) => {
      try {
        const response = await getJobExecutions(job.awsJobId);

        if (!response.executionSummaries?.length) {
          return;
        }

        // Batch update controller statuses
        const updates = response.executionSummaries.map((summary) => {
          const { status } = summary.jobExecutionSummary;
          const controllerId = parseInt(summary.thingArn.split("-").pop());

          const statusCode =
            {
              QUEUED: constants.CONTROLLER_UPDATE_STATUS.QUEUE,
              IN_PROGRESS: constants.CONTROLLER_UPDATE_STATUS.IN_PROGRESS,
              SUCCEEDED: constants.CONTROLLER_UPDATE_STATUS.DONE,
            }[status] ?? constants.CONTROLLER_UPDATE_STATUS.FAIL;

          return controllerUpdateStatusService.update(
            { controllerId },
            {
              status: statusCode,
            }
          );
        });

        await Promise.all(updates);
        await softwareJobMappingService.updateJobStatus(job.awsJobId);
      } catch (error) {
        sails.log.error(`Error fetching job executions for ${job.awsJobId}:`, error);
      }
    })
  );
}

async function getJobExecutions(jobId) {
  try {
    const params = {
      jobId: jobId,
      // maxResults: 100,
      // status: [
      //   "QUEUED",
      //   "IN_PROGRESS",
      //   "SUCCEEDED",
      //   "FAILED",
      //   "TIMED_OUT",
      //   "REJECTED",
      //   "REMOVED",
      //   "CANCELED",
      // ],
    };
    return await iotClient.send(new ListJobExecutionsForJobCommand(params));
  } catch (error) {
    sails.log.error(`Error getting job executions for ${jobId}:`, error);
    throw error;
  }
}

module.exports = {
  deployJobPackageWise: async (groupedByPackage, userId, siteId) => {
    const iotCoreRegion = process.env.IOT_CORE_REGION || "us-west-2";
    const accountId = process.env.AWS_ACCOUNT_ID || "************";
    const result = {};

    // Deploy jobs for each package
    for (const [packageName, { updateVersion, controllers }] of Object.entries(groupedByPackage)) {
      try {
        // Get job document from package
        const jobDocument = await getJobDocumentFromPackage(packageName, updateVersion);

        //if no job document is found, skip the deployment
        if (!jobDocument) {
          sails.log.error(`No job document found for package ${packageName}`);
          result[packageName] = "No job document found";
          continue;
        }

        // Generate a unique job ID
        const jobId = uuidv4();

        const targets = controllers.map(
          ({ id }) => `arn:aws:iot:${iotCoreRegion}:${accountId}:thing/${id}`
        );

        const destinationPackageVersions = [
          `arn:aws:iot:${iotCoreRegion}:${accountId}:package/${packageName}/version/${updateVersion}`,
        ];

        // Update groupedByPackage with additional info
        Object.assign(groupedByPackage[packageName], {
          jobId,
          jobDocument,
          targets,
          destinationPackageVersions,
        });

        //update database in background
        await updateDatabaseWithJobId(controllers, jobId, updateVersion, userId, siteId);

        // Deploy the job
        const deployedJob = await deployJob(
          targets,
          JSON.stringify(jobDocument),
          jobId,
          "IOT SOFTWARE UPDATE", //TODO: update later
          destinationPackageVersions
        );

        result[packageName] = deployedJob;
      } catch (err) {
        sails.log.error(`Failed to deploy job for package ${packageName}:`, err.message);
        result[packageName] = err.message;
      }
    }

    return result;
  },
  updateActiveJobStatus,
  getJobDocumentFromPackage,
  deployJob,
};
