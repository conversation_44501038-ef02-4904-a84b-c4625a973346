/**
 * softwareJobMapping.private.js
 * @description :: Private service with basic CRUD operations for alert subscriptions
 */

const constants = require("../../utils/iotCore/constants");
/**
 * Fetch active jobs from softwarejobmapping table
 * @param {*} siteId
 * @returns {Promise<Object>} Active jobs
 */
async function fetchActiveJobs(siteId) {
  try {
    // Fetch all jobs that are currently updating
    const jobs = await SoftwareJobMapping.find({
      where: {
        status: [
          constants.CONTROLLER_UPDATE_STATUS.QUEUE,
          constants.CONTROLLER_UPDATE_STATUS.IN_PROGRESS,
        ],
        siteId: siteId,
      },
      sort: "createdAt DESC",
    });

    return jobs;
  } catch (err) {
    sails.log.error("[softwareJobMapping.private] Error in fetchActiveJobs:", err);
    throw err;
  }
}

module.exports = {
  /**
   * Create a new job mapping
   * @param {Object} data - Subscription data
   * @returns {Promise<Object>} Created subscription
   */
  create: async function (data) {
    try {
      return await SoftwareJobMapping.create(data).fetch();
    } catch (err) {
      sails.log.error("[softwareJobMapping.private] Error in create:", err);
      throw err;
    }
  },
  fetchActiveJobs,
  updateJobStatus: async function (jobId) {
    try {
      // First find the software job mapping record
      const jobMapping = await SoftwareJobMapping.findOne({ awsJobId: jobId });
      if (!jobMapping) {
        throw new Error("Job mapping not found");
      }

      // Find all controller statuses for this job mapping
      const controllerStatuses = await ControllerUpdateStatus.find({
        softwareJobMappingId: jobMapping.id,
      }).select(["status"]);

      // Check if all controllers have the same status
      const firstStatus = controllerStatuses[0]?.status;
      const allSameStatus = controllerStatuses.every((c) => c.status === firstStatus);

      if (allSameStatus) {
        // Only update job mapping status if all controllers have same status
        const updatedJobMapping = await SoftwareJobMapping.updateOne(
          { awsJobId: jobId },
          { status: firstStatus }
        );
        return updatedJobMapping;
      }

      return jobMapping;
    } catch (err) {
      sails.log.error("[softwareJobMapping.private] Error in updateJobStatus:", err);
      throw err;
    }
  },
  findOne: async function (data) {
    try {
      return await SoftwareJobMapping.findOne(data);
    } catch (err) {
      sails.log.error("[softwareJobMapping.private] Error in findOne:", err);
      throw err;
    }
  },
  update: async function (query, data) {
    try {
      return await SoftwareJobMapping.update(query, data).fetch();
    } catch (err) {
      sails.log.error("[softwareJobMapping.private] Error in update:", err);
      throw err;
    }
  },
};
