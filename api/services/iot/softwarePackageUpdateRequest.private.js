/**
 * softwarePackageUpdateRequest.private.js
 * @description :: Private service with basic CRUD operations for alert subscriptions
 */

module.exports = {
  /**
   * Find all software package update requests by criteria
   * @param {Object} criteria - Query criteria
   * @returns {Promise<Array>} List of subscriptions
   */
  find: async function (criteria) {
    try {
      return await SoftwarePackageUpdateRequest.find(criteria);
    } catch (err) {
      sails.log.error("[softwarePackageUpdateRequest.private] Error in find:", err);
      throw err;
    }
  },
  /**
   * Create a new update request
   * @param {Object} data - Subscription data
   * @returns {Promise<Object>} Created subscription
   */
  create: async function (data) {
    try {
      return await SoftwarePackageUpdateRequest.create(data).fetch();
    } catch (err) {
      sails.log.error("[alertSubscription.private] Error in create:", err);
      throw err;
    }
  },
};
