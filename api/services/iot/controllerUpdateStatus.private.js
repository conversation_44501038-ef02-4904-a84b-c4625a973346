/**
 * controllerUpdateStatus.private.js
 * @description :: Private service with basic CRUD operations for alert subscriptions
 */

const flaverr = require("flaverr");
const constants = require("../../utils/iotCore/constants");

async function find(data) {
  try {
    return await ControllerUpdateStatus.find(data);
  } catch (err) {
    sails.log.error("[controllerUpdateStatus.private] Error in find:", err);
    throw flaverr(
      "E_BAD_REQUEST",
      new Error(`[controllerUpdateStatus.private] Error in find: ${err}`)
    );
  }
}

module.exports = {
  find,
  /**
   * Create a new controller update status
   * @param {Object} data - Subscription data
   * @returns {Promise<Object>} Created subscription
   */
  create: async function (data) {
    try {
      return await ControllerUpdateStatus.create(data).fetch();
    } catch (err) {
      sails.log.error("[controllerUpdateStatus.private] Error in create:", err);
      throw flaverr(
        "E_BAD_REQUEST",
        new Error(`[controllerUpdateStatus.private] Error in create: ${err}`)
      );
    }
  },

  /**
   * Get all controller update statuses
   * @returns {Promise<Array>} List of controller update statuses
   */
  getUpdatingMasterControllers: async function (siteId) {
    try {
      const result = await ControllerUpdateStatus.find({
        siteId,
        status: constants.CONTROLLER_UPDATE_STATUS.QUEUE,
      });
      return result;
    } catch (err) {
      sails.log.error("[controllerUpdateStatus.private] Error in getUpdatingControllers:", err);
      throw flaverr(
        "E_BAD_REQUEST",
        new Error(`[controllerUpdateStatus.private] Error in getUpdatingControllers: ${err}`)
      );
    }
  },

  /**
   * Get all finished controller update statuses
   * @returns {Promise<Array>} List of finished controller update statuses
   */
  getFinishedMasterControllers: async function (siteId) {
    try {

      const result = await sails.getDatastore("postgres").sendNativeQuery(
        `
          SELECT DISTINCT ON (controller_id) *
          FROM controller_update_status
          WHERE site_id = $1 AND status IN ($2, $3)
          ORDER BY controller_id, created_at DESC
        `,
        [siteId, constants.CONTROLLER_UPDATE_STATUS.DONE, constants.CONTROLLER_UPDATE_STATUS.FAIL]
      );

      return result?.rows || [];
    } catch (err) {
      sails.log.error(
        "[controllerUpdateStatus.private] Error in getFinishedMasterControllers:",
        err
      );
      throw flaverr(
        "E_BAD_REQUEST",
        new Error(`[controllerUpdateStatus.private] Error in getFinishedMasterControllers: ${err}`)
      );
    }
  },

  /**
   * Create multiple controller update statuses
   * @param {Array} data - Array of controller update status data
   * @returns {Promise<Array>} List of created controller update statuses
   */
  createEach: async function (data) {
    try {
      return await ControllerUpdateStatus.createEach(data);
    } catch (err) {
      sails.log.error("[controllerUpdateStatus.private] Error in createEach:", err);
      throw flaverr(
        "E_BAD_REQUEST",
        new Error(`[controllerUpdateStatus.private] Error in createEach: ${err}`)
      );
    }
  },

  /**
   * Find a single controller update status
   * @param {Object} query - Query object
   * @returns {Promise<Object>} Found controller update status
   */
  findOne: async function (query) {
    try {
      return await ControllerUpdateStatus.findOne(query);
    } catch (err) {
      sails.log.error("[controllerUpdateStatus.private] Error in findOne:", err);
      throw err;
    }
  },

  /**
   * Update a controller
   * @param {Object} query - Query object
   * @param {Object} data - Update data
   * @returns {Promise<Object>} Updated controller update status
   */
  update: async function (query, data) {
    try {
      return await ControllerUpdateStatus.update(query, data).fetch();
    } catch (err) {
      sails.log.error("[controllerUpdateStatus.private] Error in update:", err);
      throw err;
    }
  },
};
