const IotCoreCommunicationService = require("../../services/iotCore/IotCoreCommunication");
const AWSIoTCoreService = require("../../services/iotCore/AWSIoTCoreService");
const iotCoreComInstance = new IotCoreCommunicationService(new AWSIoTCoreService());
const softwarePackagesService = require("../../services/iot/softwarePackages.service");
const devicePublic = require("../../services/device/device.public");
const constants = require("../../utils/iotCore/constants");
const influxService = require("../../services/influx/influx.service");
const sentry = require("../../services/logTransport/sentry.service");
const dynamokeystorePublic = require("../../services/dynamokeystore/dynamokeystore.public");
const flaverr = require("flaverr");
const controllerUpdateStatusService = require("./controllerUpdateStatus.service");
const softwareJobMappingPublic = require("../../services/iot/softwareJobMapping.public");
const controllerUpdateStatusPublic = require("../../services/iot/controllerUpdateStatus.public");
const devConfigPublic = require("../devConfig/devConfig.public");
const devSyncConstants = require("../../utils/devConfig/Constants");
// require("../../api/utils/devConfig/Constants.js");

function mapLatestVersions(packageMapping, versionData) {
  const controllerTypeToVersion = {};
  for (const [packageName, deviceTypes] of Object.entries(packageMapping)) {
    const version = versionData[packageName]?.latestVersion || constants.DEFAULT_CONTROLLER_VERSION;
    deviceTypes.forEach((deviceType) => {
      controllerTypeToVersion[deviceType] = {
        version: version,
        publishedVersions: versionData[packageName]?.publishedVersions,
      };
    });
  }
  return controllerTypeToVersion;
}

async function compareVersions(masterControllerWithVersion, controllerTypeToVersion) {
  return masterControllerWithVersion.map((device) => {
    device.availableVersion = constants.DEFAULT_CONTROLLER_VERSION;
    device.updateAvailable = false; // Default to no update available
    device.publishedVersions = [];
    if (controllerTypeToVersion[device.controllerType]) {
      device.availableVersion = controllerTypeToVersion[device.controllerType].version;
      device.publishedVersions = controllerTypeToVersion[device.controllerType].publishedVersions;
      // Compare versions using the helper function
      device.updateAvailable = isVersionNewer(device.currentVersion, device.availableVersion);
    }
    return device;
  });
}

/**
 * Compares two semantic version strings (e.g., "v1.1.1" vs "v1.2.2").
 * @param {string} currentVersion - The current version (e.g., "v1.1.1").
 * @param {string} availableVersion - The available version (e.g., "v1.2.2").
 * @returns {boolean} - Returns true if an update is available.
 */
function isVersionNewer(currentVersion, availableVersion) {
  // Remove 'v' prefix if present
  const cleanCurrent = currentVersion.replace(/^v/, "");
  const cleanAvailable = availableVersion.replace(/^v/, "");

  // Convert to arrays of numbers
  const currentParts = cleanCurrent.split(".").map(Number);
  const availableParts = cleanAvailable.split(".").map(Number);

  // Compare version parts
  for (let i = 0; i < Math.max(currentParts.length, availableParts.length); i++) {
    const cur = currentParts[i] || 0;
    const avail = availableParts[i] || 0;

    if (avail > cur) return true; // Update available
    if (avail < cur) return false; // Current is newer or same
  }

  return false; // Versions are equal
}

async function getMasterControllerWithVersion(masterControllers, packageMapping) {
  const results = await Promise.allSettled(
    masterControllers.map(({ controllerId }) =>
      iotCoreComInstance.fetchShadowVersionByControllerId(controllerId, packageMapping)
    )
  );

  const masterControllerWithVersion = masterControllers.map(({ controllerId, ...rest }, index) => {
    const result = results[index];

    const version =
      result.status === "fulfilled" ? result.value : constants.DEFAULT_CONTROLLER_CURRENT_VERSION;

    return {
      id: controllerId,
      ...rest,
      currentVersion: version || constants.DEFAULT_CONTROLLER_CURRENT_VERSION,
    };
  });

  return masterControllerWithVersion;
}

async function getMasterControllersBySiteId(siteId, deviceId = null) {
  const masterControllerList = await devicePublic.getMasterControllersBySiteId(siteId, deviceId);

  if (!masterControllerList || masterControllerList.length === 0) {
    sentry.captureException(
      new Error(`iot > getMasterController > No master controllers found for siteId: ${siteId}`),
      {
        extra: { siteId, deviceId },
      }
    );
    sails.log.error(
      `iot > getMasterController > No master controllers found for siteId: ${siteId}`
    );
    throw flaverr("E_BAD_REQUEST", new Error(`No master controllers found for siteId: ${siteId}`));
  }

  // Filter and sort the master controllers
  const masterControllers = masterControllerList
    .map((device) => {
      if (device.deviceId && device.name) {
        return {
          controllerId: device.deviceId,
          name: device.name,
          controllerType: `${device.deviceType}_${device.hardwareVer}`,
          controllerMasterType: device.deviceType,
        };
      }
    })
    .filter(Boolean)
    .sort((a, b) => a.name.localeCompare(b.name));

  return masterControllers;
}

// get ip address from influx
async function getAllocatedControllerIpBySiteId(siteId) {
  const ipAddressQuery = `
        from(bucket: "iot-metrics")
      |> range(start: -1h)
      |> filter(fn: (r) => r["_measurement"] == "networkinfo")
      |> filter(fn: (r) => r["siteid"] == "${siteId}")
      |> filter(fn: (r) => r["_field"] == "ip")
      |> group(columns: ["controllerid"])
      |> last()
      |> yield(name: "last")
    `;

  const ipAddressData = await influxService.runQuery(
    ipAddressQuery,
    {
      debug: true,
    },
    "iot_influxdb"
  );

  return ipAddressData || [];
}

// get online/offline from influx
async function getControllerStatusBySiteId(siteId) {
  const statusQuery = `from(bucket: "iot-cloud-metrics")
      |> range(start:-15m)
      |> filter(fn: (r) => r["_measurement"] == "controllerconnectivity")
      |> filter(fn: (r) => r["siteid"] == "${siteId}")
      |> filter(fn: (r) => r["_field"] == "status")
      |> last()
      |> drop(columns: ["_measurement", "siteid","awsiotregistered","controllertype", "result"])
      |> pivot(rowKey:["_time"], columnKey: ["_field"], valueColumn: "_value")
      |> yield(name: "last")`;

  const statusData = await influxService.runQuery(statusQuery, { debug: true }, "iot_influxdb");

  if (!Array.isArray(statusData) || statusData.length === 0) {
    sails.log.error(`iot > getControllerStatusBySiteId > No status data for siteId: ${siteId}`);
  }

  return statusData || [];
}
function extractResult(result, label) {
  if (result.status === "fulfilled") {
    return result.value;
  }
  sails.log.error(`iot > Error > Failed to fetch ${label}:`, result.reason);
  sentry.captureException(new Error(`iot > getMasterController > Failed to fetch ${label}`), {
    extra: { error: result.reason },
  });
  return [];
}

async function fetchMasterControllersData(
  siteId,
  fetchIpData = true,
  statusSort = null,
  fetchDevConfig = true,
  devConfigStatusSort = null
) {
  const [
    masterControllerList,
    ipAddressDataResult,
    controllerStatusDataResult,
    devConfigDataResult,
  ] = await Promise.allSettled([
    getMasterControllersBySiteId(siteId),
    fetchIpData ? getAllocatedControllerIpBySiteId(siteId) : Promise.resolve([]),
    getControllerStatusBySiteId(siteId),
    fetchDevConfig ? devConfigPublic.fetchDevConfigBySiteId(siteId) : Promise.resolve([]),
  ]);

  const masterControllersRaw = extractResult(masterControllerList, "master controllers");
  const ipAddressData = fetchIpData ? extractResult(ipAddressDataResult, "IP address data") : [];
  const controllerStatusData = extractResult(controllerStatusDataResult, "controller status data");
  const devConfigData = fetchDevConfig ? extractResult(devConfigDataResult, "dev config data") : [];

  const devConfigMap = Object.fromEntries(
    devConfigData
      .filter((d) => d.controllerId)
      .map((d) => [
        d.controllerId,
        {
          // update status for dev config success - synced, fail - sync fail, else sync available
          status: (() => {
            if (d.status === devSyncConstants.DEV_CONFIG_STATUS.SUCCESS) {
              return constants.SYNC_STATUS.SYNCED;
            }
            if (d.status === devSyncConstants.DEV_CONFIG_STATUS.FAILED) {
              return constants.SYNC_STATUS.SYNC_FAILED;
            }
            return constants.SYNC_STATUS.SYNC_AVAILABLE;
          })(),
        },
      ])
  );

  // Map controller status and IP by controller ID
  const statusMap = Object.fromEntries(
    controllerStatusData.filter((d) => d.controllerid).map((d) => [d.controllerid, d.status])
  );

  const ipMap = Object.fromEntries(
    ipAddressData.filter((d) => d.controllerid && d._value).map((d) => [d.controllerid, d._value])
  );

  // Merge status and IP into master controllers
  const masterControllers = masterControllersRaw.map((controller) => {
    const controllerId = controller.controllerId?.toString();
    return {
      ...controller,
      status: statusMap[controllerId] ?? null,
      ip: fetchIpData ? ipMap[controllerId] ?? null : undefined,
      devConfigStatus: fetchDevConfig
        ? devConfigMap[controllerId]?.status ?? constants.SYNC_STATUS.SYNC_AVAILABLE
        : undefined,
    };
  });

  if (statusSort === 1) {
    masterControllers.sort((a, b) => {
      if (a.status === 1 && b.status !== 1) return -1;
      if (a.status !== 1 && b.status === 1) return 1;
      return 0;
    });
  } else if (statusSort === 0) {
    masterControllers.sort((a, b) => {
      if (a.status === 0 && b.status !== 0) return -1;
      if (a.status !== 0 && b.status === 0) return 1;
      return 0;
    });
  }

  if (devConfigStatusSort === 0) {
    // Ascending: 0 → 1 → 2
    masterControllers.sort((a, b) => a.devConfigStatus - b.devConfigStatus);
  } else if (devConfigStatusSort === 1) {
    // Descending: 2 → 1 → 0
    masterControllers.sort((a, b) => b.devConfigStatus - a.devConfigStatus);
  }

  return masterControllers;
}

async function groupMasterControllersByPackaage(controllerList, siteId) {
  const [masterControllerList, packageMappingResult, versionDataResult] = await Promise.allSettled([
    getMasterControllersBySiteId(siteId),
    dynamokeystorePublic.findOne({ key: `packageMapping` }),
  ]);

  const masterControllersRaw = extractResult(masterControllerList, "master controllers");
  const packageMappingRaw = extractResult(packageMappingResult, "package mapping", {});

  let packageMapping = packageMappingRaw?.value || {};
  const groupedByPackage = {};

  if (Object.keys(packageMapping).length === 0 || masterControllersRaw.length === 0) {
    sentry.captureException(new Error(`iot > getMasterController > Failed to fetch metadata`), {
      extra: { error: "Error finding meta data for package." },
    });
    sails.log.error("iot > getMasterController > Error finding meta data for package");
    throw flaverr("E_UNPROCESSABLE_ENTITY", new Error("Error finding meta data for package."));
  }

  const controllers = controllerList
    .map((cntrl) => {
      if (!cntrl || typeof cntrl !== "object") {
        sails.log.error("iot > updateMasterController > Invalid controller data format");
        return null;
      }

      if (!cntrl.controllerId || !cntrl.targetSoftwareVersion) {
        sails.log.error("iot > updateMasterController > Missing required controller properties");
        return null;
      }

      const controller = masterControllersRaw.find(
        (c) => Number(c.controllerId) === cntrl.controllerId
      );

      if (!controller) return null;

      const deviceType = controller?.controllerType;
      if (!deviceType) {
        sails.log.error(
          `iot > updateMasterController > Missing controller type for controller ${controller?.controllerId}`
        );
        return null;
      }

      return {
        id: `controller-${controller.controllerId}`,
        controllerType: deviceType,
        targetSoftwareVersion: cntrl.targetSoftwareVersion,
        currentSoftwareVersion: cntrl.currentSoftwareVersion,
      };
    })
    .filter(Boolean); // Removes nulls in case some controllers aren't found

  if (controllers.length === 0) {
    sails.log.error(
      "iot > updateMasterController > No valid controllers found for the provided IDs."
    );
    throw flaverr("E_BAD_REQUEST", new Error("No valid controllers found for the provided IDs."));
  }

  // Combine reverse mapping and grouping
  for (const controller of controllers) {
    const { controllerType, targetSoftwareVersion } = controller;

    if (!targetSoftwareVersion) {
      throw flaverr(
        "E_BAD_REQUEST",
        new Error(`Missing target software version for controller ${controller.id}`)
      );
    }

    // Check if the controllerType exists in the packageMapping
    const packageName = Object.entries(packageMapping).find(([_, types]) =>
      types.includes(controllerType)
    )?.[0];

    if (!packageName) {
      sentry.captureException(
        new Error(
          `iot > updateMasterController > Unknown controllerType "${controllerType}" for controller ${controller.id}`
        ),
        {
          extra: {
            error: `Unknown controllerType "${controllerType}" for controller ${controller.id}`,
          },
        }
      );
      sails.log.error(
        `iot > updateMasterController > Unknown controllerType "${controllerType}" for controller ${controller.id}`
      );
      throw flaverr(
        "E_BAD_REQUEST",
        new Error(`Unknown controllerType "${controllerType}" for controller ${controller.id}`)
      );
    }

    const updateVersion = targetSoftwareVersion;

    if (!groupedByPackage[packageName]) {
      groupedByPackage[packageName] = {
        updateVersion,
        controllers: [controller],
      };
    } else {
      const expectedVersion = groupedByPackage[packageName].updateVersion;
      if (expectedVersion !== updateVersion) {
        throw flaverr(
          "E_BAD_REQUEST",
          new Error(
            `Inconsistent update versions for package "${packageName}". Expected "${expectedVersion}", got "${updateVersion}".`
          )
        );
      }
      groupedByPackage[packageName].controllers.push(controller);
    }
  }

  if (Object.keys(groupedByPackage).length === 0) {
    throw flaverr("E_BAD_REQUEST", new Error("No valid package groups could be created"));
  }

  return groupedByPackage;
}

function extractResult(result, label) {
  if (result.status === "fulfilled") {
    return result.value;
  }
  sails.log.error(`iot > Error > Failed to fetch ${label}:`, result.reason);
  sentry.captureException(new Error(`iot > getMasterController > Failed to fetch ${label}`), {
    extra: { error: result.reason },
  });
  return [];
}

async function getOutdatedControllers(siteId) {
  const finishedControllers = await controllerUpdateStatusService.getFinishedMasterControllers(
    siteId
  );

  return finishedControllers.filter((controller) => {
    controller.isOutdated =
      controller.current_version !== controller.expected_version &&
      controller.current_version < controller.expected_version;
    return controller.isOutdated;
  });
}

async function validateJobId(jobId, siteId, status) {
  const job = await softwareJobMappingPublic.findOne({
    awsJobId: jobId,
    siteId,
  });

  if (!job) {
    sails.log.error(`iot > cancelJob > Job ID ${jobId} not found for site ID ${siteId}`);
    throw flaverr("E_NOT_FOUND", new Error(`Job ID ${jobId} not found for site ID ${siteId}}`));
  }

  // Check if any controller status for this job is cancelled
  const controllerStatus = await controllerUpdateStatusPublic.find({
    softwareJobMappingId: job.id,
  });

  const allCancelled = controllerStatus.every((status) => status.status === "cancelled");

  if (allCancelled) {
    sails.log.error(`iot > cancelJob > Job ID ${job.awsJobId} is already cancelled`);
    throw flaverr("E_BAD_REQUEST", new Error(`Job ID ${job.awsJobId} is already cancelled`));
  }

  return job;
}

/**
 * Update the status of a controller software job
 * @param {Object} job - The job object
 * @param {string} status - The status to update to
 * @returns {Promise<Object>} The updated controller update status
 */
async function updateControllerUpdateStatus(job, status) {
  if (!job?.id) {
    throw flaverr("E_BAD_REQUEST", new Error("Invalid job: missing job ID"));
  }

  await Promise.all([
    softwareJobMappingPublic.update({ id: job.id }, { status: status }),
    controllerUpdateStatusPublic.update({ softwareJobMappingId: job.id }, { status: status }),
  ]);
}

module.exports = {
  fetchMasterControllersData,
  extractResult,
  groupMasterControllersByPackaage,
  getControllerStatusBySiteId,
  /**
   * Fetches the current version for each master controller and compares it with the latest available version.
   * @param {Array} masterControllers - The list of master controllers to check.
   * @param {Object} packageMapping - The mapping of package names to device types.
   * @returns {Promise<Array>} - The list of master controllers with version comparison results.
   */
  async fetchAndCompareMasterControllerVersions(masterControllers, packageMapping) {
    // Fetch current version for each device
    const masterControllerWithVersion = await getMasterControllerWithVersion(
      masterControllers,
      packageMapping
    );

    // Fetch latest available package versions
    const versionData = await softwarePackagesService.fetchPackagesWithDetails();

    // Map the latest available versions to device types
    const controllerTypeToVersion = mapLatestVersions(packageMapping, versionData);

    // Compare current versions with available versions
    const comparedVersions = await compareVersions(
      masterControllerWithVersion,
      controllerTypeToVersion
    );

    const controllerIds = comparedVersions.map((c) => c.id);

    // Avoid if controllerIds is empty
    if (controllerIds.length === 0) return comparedVersions;

    // Use parameterized query
    const placeholders = controllerIds.map((_, index) => `$${index + 1}`).join(",");

    //get the latest update status for each controller
    const updateStatusesResult = await sails.getDatastore("postgres").sendNativeQuery(
      `
    SELECT DISTINCT ON (controller_id) *
    FROM controller_update_status
    WHERE controller_id IN (${placeholders})
    ORDER BY controller_id, created_at DESC
  `,
      controllerIds
    );

    // Create a lookup map from the result
    const latestStatusMap = new Map();
    for (const status of updateStatusesResult.rows) {
      latestStatusMap.set(status.controller_id, status);
    }

    // Update each controller's versionStatus
    for (const controller of comparedVersions) {
      const latestStatus = latestStatusMap.get(controller.id);

      if (latestStatus?.status === constants.CONTROLLER_UPDATE_STATUS.FAIL) {
        controller.versionStatus = constants.UPDATE_STATUS.FAILED;
      } else if (controller.updateAvailable) {
        controller.versionStatus = constants.UPDATE_STATUS.UPDATE_AVAILABLE;
      } else {
        controller.versionStatus = constants.CONTROLLER_UPDATE_STATUS.DONE;
      }
    }

    return comparedVersions;
  },
  getFilteredMasterControllers(
    masterControllers,
    searchQuery = null,
    controllerType = null,
    status = null,
    controllerId = null,
    version = null,
    versionStatus = null,
    devConfig = null
  ) {
    //controller id filter
    if (controllerId && Array.isArray(controllerId) && controllerId.length > 0) {
      masterControllers = masterControllers.filter(
        (ctrl) => controllerId.includes(ctrl.controllerId || ctrl.id) //check ctrl.id if controllerId is not present, used for version sorting/filtering cases
      );
    }

    //controller name search
    if (searchQuery) {
      masterControllers = masterControllers.filter((ctrl) =>
        ctrl.name.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }

    //controller type filter
    if (Array.isArray(controllerType) && controllerType.length > 0) {
      masterControllers = masterControllers.filter((ctrl) =>
        controllerType.includes(ctrl.controllerMasterType)
      );
    }

    //status filter
    if (status !== undefined && status !== null && Array.isArray(status) && status.length > 0) {
      masterControllers = masterControllers.filter((ctrl) => status.includes(ctrl.status));
    }

    if (version && Array.isArray(version) && version.length > 0) {
      masterControllers = masterControllers.filter((ctrl) => version.includes(ctrl.currentVersion));
    }

    if (
      versionStatus !== undefined &&
      versionStatus !== null &&
      Array.isArray(versionStatus) &&
      versionStatus.length > 0
    ) {
      masterControllers = masterControllers.filter((ctrl) =>
        versionStatus.includes(ctrl.versionStatus)
      );
    }

    //filter dev config status
    if (
      devConfig !== undefined &&
      devConfig !== null &&
      Array.isArray(devConfig) &&
      devConfig.length > 0
    ) {
      masterControllers = masterControllers.filter((ctrl) =>
        devConfig.includes(ctrl.devConfigStatus)
      );
    }

    return masterControllers;
  },
  getMasterControllersBySiteId: getMasterControllersBySiteId,
  getOutdatedControllers,
  validateJobId: validateJobId,
  updateControllerUpdateStatus: updateControllerUpdateStatus,
  getSiteUnsyncedControllers,
};


/**
 * Fetch site all controllers whose dev config not synced yet
 * @param {String} siteId 
 * @returns {Array<controllerId>}
 */
async function getSiteUnsyncedControllers(siteId) {
  const fetchSiteControllerWithStatus = await fetchMasterControllersData(siteId, fetchIpData=false);
  if (_.isEmpty(fetchSiteControllerWithStatus)) return [];
  
  const syncAvailableControllerIds = fetchSiteControllerWithStatus.reduce((acc, controller) => {
      const isOutOfSync = [
        constants.SYNC_STATUS.SYNC_AVAILABLE,
        constants.SYNC_STATUS.SYNC_FAILED,
      ].includes(controller.devConfigStatus);

      if (isOutOfSync && controller.controllerId) {
        acc.push(controller.controllerId);
      }

       return acc;
  }, []);
 return syncAvailableControllerIds;
}