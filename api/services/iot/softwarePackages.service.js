const {
  IoTClient,
  ListPackagesCommand,
  ListPackageVersionsCommand,
  CancelJobCommand,
} = require("@aws-sdk/client-iot");
const sentry = require("../../services/logTransport/sentry.service");
const constants = require("../../utils/iotCore/constants");
const iotService = require("./iot.service");

// Initialize IoT Client with the correct region
const iotClient = new IoTClient({ region: process.env.IOT_CORE_REGION || "ap-south-1" });

//List available packages from AWS IoT
async function listPackages() {
  try {
    const command = new ListPackagesCommand({});
    const packagesResponse = await iotClient.send(command);
    return packagesResponse.packageSummaries || [];
  } catch (error) {
    sentry.captureException(new Error("iot > listPackages > Error fetching packages"), {
      extra: { error },
    });
    sails.log.error("iot > listPackages > Error fetching packages", error);
    return [];
  }
}

//Fetch package details (version information)
async function getPackageDetails(packageName) {
  try {
    const command = new ListPackageVersionsCommand({ packageName });
    const response = await iotClient.send(command);

    // Check if the response contains packageVersionSummaries
    if (!response.packageVersionSummaries || response.packageVersionSummaries.length === 0) {
      return { packageName, latestVersion: constants.DEFAULT_CONTROLLER_VERSION };
    }

    // Filter out the package versions that are PUBLISHED
    const publishedVersions = response.packageVersionSummaries.reduce(
      (acc, pkg) => (pkg.status === "PUBLISHED" ? [...acc, pkg.versionName] : acc),
      []
    );

    const latestPublished = {
      versionName:
        publishedVersions.sort((a, b) => new Date(b.creationDate) - new Date(a.creationDate))[0] ||
        constants.DEFAULT_CONTROLLER_VERSION,
    };

    return {
      packageName,
      latestVersion: latestPublished.versionName,
      publishedVersions,
    };
  } catch (error) {
    sails.log.error(
      "iot > listPackages > Error fetching details for package ${packageName}",
      error
    );
    return { packageName, latestVersion: iotService.defaultControllerVersion };
  }
}

async function cancelIoTJob(jobId) {
  const command = new CancelJobCommand({
    jobId,
    // reasonCode: "CancelledByUser", // optional, for tracking
    // comment: "Cancelled via dejoule API", // optional
  });

  try {
    await iotClient.send(command);
  } catch (error) {
    sails.log.error("iot > cancelIoTJob > Error cancelling job", error);
  }
}

module.exports = {
  // 5️⃣ Get the latest package versions
  fetchPackagesWithDetails: async () => {
    const packages = await listPackages();
    if (!packages.length) {
      console.log("No packages found.");
      return {};
    }

    const packageDetails = await Promise.all(
      packages.map((pkg) => getPackageDetails(pkg.packageName)) // Pass only the packageName
    );

    // Create a map to store both latest version and published versions for each package
    const packageMap = {};
    packageDetails.forEach(({ packageName, latestVersion, publishedVersions }) => {
      packageMap[packageName] = {
        latestVersion,
        publishedVersions: publishedVersions || [],
      };
    });

    return packageMap;
  },
  cancelIoTJob,
};
