const datadevice = require("./datadevice.private");
const globalHelpers = require("../../utils/globalhelper");
const parameterService = require("../parameter/parameter.public");
const utils = require("../../utils/datadevice/utils");
const influx = require("../influx/enterprise/influx.service");
const { dataParameter } = require("../component/cachingService");
const moment = require('moment');
const momentTz = require('moment-timezone');
const axios = require('axios');
const UserSiteMapPublic = require("../userSiteMap/userSiteMap.public");
const DynamoKeyStoreService = require("../dynamokeystore/dynamokeystore.public");
const influxEnterpriseService = require("../influx/enterprise/influx.public");
const dashboardDataDevice = require('./dashboard-datadevice.service');
const cacheService = require("../cache/cache.public");
const flaverr = require('flaverr');
const sentry = require("../../services/logTransport/sentry.service");

const BUCKET = 'device_component/autogen';
const MEASUREMENT = 'device';

const dataDeviceService = {
  create: datadevice.create,
  find: datadevice.find,
  findOne: datadevice.findOne,
  update: datadevice.update,
  delete: datadevice.delete,

  /**
   * Get data from datadevice table between 2 timestamps.
   * It queries data inclusive of both start and endDate.
   * @param {string} deviceId Device/Component id to get data of
   * @param {string} startTime Start time to get data FROM
   * @param {string} endTime End time to get data TO
   * @param {boolean} sortInDescending Pass true to return results in descending timestamp. Defaults to false, ie, results in ascending order of timestamp.
   */
  getDataBetweenTwoTimeStamps: async function (deviceId, startTime, endTime, sortInDescending = false, siteId) {
    if (
      !globalHelpers.isValidDateTime(startTime) ||
      !globalHelpers.isValidDateTime(endTime)
    ) {
      return { problems: ["Invalid start/endtime"] };
    }
    let startTimeMoment = globalHelpers.toMoment(startTime);
    let endTimeMoment = globalHelpers.toMoment(endTime);
    let differenceInDays = endTimeMoment.diff(startTimeMoment, "days");

    if (startTimeMoment > endTimeMoment) {
      return { problems: ["start cannot be greater than endtime"] };
    }
    if (differenceInDays > utils.MAX_ALLOWED_DAYS) {
      return {
        problems: [
          `Cannot query data more than ${utils.MAX_ALLOWED_DAYS} days`,
        ],
      };
    }
    const timezoneOffset = await sails.helpers.getSiteTimezone.with({siteId, timezoneFormat:'utcOffsetInMinute'});
    let timestamp = { in: [startTime, endTime] };
    let deviceData;

    if (
      process.env.PRIMARY_DATA_SOURCE &&
      process.env.PRIMARY_DATA_SOURCE === "influx"
    ) {
      const _startTime = moment(startTime, "YYYY-MM-DD HH:mm:ss")
        .utcOffset(timezoneOffset)
        .format("YYYY-MM-DDTHH:mm:ssZ");
      const _endTime = moment(endTime, "YYYY-MM-DD HH:mm:ss")
        .utcOffset(timezoneOffset)
        .add('1','seconds')
        .format("YYYY-MM-DDTHH:mm:ssZ");
      deviceData = await this.getDataBetweenTwoTimeStampsFromInfluxdb(
        deviceId,
        _startTime,
        _endTime,
        sortInDescending
      );
      return deviceData;
    }

    try {
      const sortParams = sortInDescending ? "timestamp DESC" : null;
      deviceData = await this.find({ deviceId, timestamp }, sortParams);
    } catch (e) {
      throw e;
    }
    return deviceData;
  },

  /**
   * Query data between timestamp+positive and timestamp-negative values and return the nearest data to timestamp
   * @param {string} deviceId unique device/componentId to get the data of
   * @param {string} timestamp timestamp dejoule/unix format to get nearest data of
   * @param {integer} positive Max upperLimit to query data in between of
   * @param {integer} negative Max lowelimit to query data in between of
   *
   * @return  null if no data else data object
   */
  getDataNearbyTimeStamp: async function (
    deviceId,
    timestamp,
    positive = 2,
    negative = 2,
    siteId

  ) {
    let tsUpperLimit, tsLowerLimit, dataPoints, deviceData;

    if (!globalHelpers.isValidDateTime(timestamp)) {
      return { problems: ["Invalid timestamp"] };
    }
    timestamp = globalHelpers.formatDateTime(timestamp);
    tsUpperLimit = globalHelpers
      .toMoment(timestamp)
      .add(positive, "minute")
      .format();
    tsLowerLimit = globalHelpers
      .toMoment(timestamp)
      .subtract(negative, "minute")
      .format();

    try {
      dataPoints = await this.getDataBetweenTwoTimeStamps(
        deviceId,
        tsLowerLimit,
        tsUpperLimit,
        false,
        siteId
      );
    } catch (e) {
      throw e;
    }
    if (!dataPoints || dataPoints.length === 0) {
      return null;
    }
    deviceData = dataPoints.find(
      (dataPoint) => dataPoint["timestamp"] === timestamp
    ); // find if exact data at that time exist
    if (deviceData) {
      return deviceData;
    } else {
      nearestDataPoint = utils.getNearestDataToTimestamp(timestamp, dataPoints);
      nearestDataPoint["timestamp"] = timestamp; // set timestamp back to whats required
      return nearestDataPoint;
    }
  },

  /**
   * Get only required parameter's data between 2 timestamp in preferred unit.
   * example unitPreference = {delTemperature: 'K'},
   * meaning whichever param have paramGroup = 'delTemperature' should return value in 'K'.
   * dau of 'delTemperature' is degC so we will convert all data to 'K' if specified in 'params' array
   * @param {string} deviceId unique device/component id to get data of
   * @param {array} params Array of parameters to look for in deviceData packet
   * @param {string} siteId site id
   * @param {string} startTime start time to
   * @param {string} endTime end
   * @param {object} unitPreference Required unit preference of params. i.e {paramGroup: prefernce}.
   *
   * @returns either array of deviceData: [{data, ts}, ...] OR an Object with {problems: [problem]}
   */
  getDeviceParamsDataBetween2TimestampInPreferredUnit: async function (
    deviceId,
    params,
    siteId,
    startTime,
    endTime,
    unitPreference
  ) {
    if (
      globalHelpers.isNullish(unitPreference) ||
      unitPreference.constructor.name !== "Object"
    ) {
      unitPreference = {};
    }

    const sortInDescending = false;
    let deviceDatas = await this.getDataBetweenTwoTimeStamps(
      deviceId,
      startTime,
      endTime,
      sortInDescending,
      siteId
    );
    if (deviceDatas.problems) {
      return deviceDatas;
    }

    let filteredDeviceParamsData = deviceDatas.map((deviceData) => {
      return utils.getParamDataFromDeviceDataPacket(deviceData, params);
    });

    for (let param of params) {
      let { dau, paramGroup } =
        await parameterService.getParamsDauAndParamgroup(
          siteId,
          deviceId,
          param
        );
      let unitPreferenceForParam = unitPreference[paramGroup];

      if (unitPreferenceForParam && unitPreferenceForParam !== dau) {
        filteredDeviceParamsData.forEach((deviceData) => {
          deviceData["data"][param] = globalHelpers.convertUnits(
            dau, // FROM unit
            unitPreferenceForParam, // TO Unit
            deviceData["data"][param] // value to convert FROM - to - TO
          );
        });
      }
    }
    return filteredDeviceParamsData;
  },

  /**
   * Get only consumption data between 2 timestamp in kwh or kvah.
   * example unitPreference = 'kwh',
   * Query data of deviceId between 2 time and return kwh value. so
   * [{ts, data:{kwh: value/null }}, ...]
   * @param {string} deviceId unique device/component id to get data of
   * @param {string} siteId site id
   * @param {string} startTime start time to
   * @param {string} endTime end
   * @param {string} unitPreference Required unit preference kvah oR KwH while making the API
   *
   * @returns either array of deviceData: [{data, ts}, ...] OR an Object with {problems: [problem]}
   */
  getDeviceConsumptionDataBetween2TimestampInPreferredUnit: async function (
    deviceId,
    startTime,
    endTime,
    unitPreference = "kvah"
  ) {
    if (utils.isValidConsumptionUnit(unitPreference) === false) {
      return { problems: ["Nat a valid unit preference for consumption"] };
    }

    let deviceDatas = await this.getDataBetweenTwoTimeStamps(
      deviceId,
      startTime,
      endTime
    );
    if (deviceDatas.problems) {
      return deviceDatas;
    }

    for (let deviceData of deviceDatas) {
      let dataPckt = globalHelpers.toJson(deviceData["data"]);
      if (dataPckt === undefined) dataPckt = { [unitPreference]: null };
      else {
        let consumptionValue =
          utils.getConsumptionFromDataPacketInUnitPreference(
            dataPckt,
            unitPreference
          );
        dataPckt[unitPreference] = consumptionValue;
      }
      deviceData["data"] = dataPckt;
    }
    return deviceDatas;
  },

  getGraphsMetaDataBetween2Timestamps: async function (
    deviceId,
    startTime,
    endTime,
    params,
    type
  ) {
    let metaDataObj = { user: [], maintainance: [], modes: {} };

    if (type !== "line") {
      return metaDataObj;
    }

    return metaDataObj; // for time being
  },

  /**
   * Get consumption of emDevice on that particulat day. so consumption as dayX = consumption(dayXendDate) - consumption(dayXstartDate). (here dayXstartDate = endDate - 1day)
   * @param {string} deviceId Single Energy meter Device Id to get consumption of
   * @param {string} endTime Timestamp to get data of. Its endTime bcz consumption on this date will be consumption(endDate) - consumption(startDate). (here startDate = endDate - 1day)
   * @param {object} unitPreference Required unit preference of params. i.e {paramGroup: prefernce}.
   * @param {integer} positive upper limit in minutes to add to in timestamp
   * @param {integer} negative lower limit in minutes to add to in timestamp
   */
  getEMConsumptionForDayInPreferredUnit: async function (
    emDeviceId,
    endTime,
    unitPreference = "kvah",
    positive = 5,
    negative = 5
  ) {
    if (globalHelpers.isValidDateTime(endTime) === false) {
      return { problems: ["Date is not valid"] };
    }
    if (utils.isValidConsumptionUnit(unitPreference) === false) {
      return { problems: ["Not a valid consumption unit"] };
    }

    let endTimeMoment = globalHelpers.toMoment(endTime);
    let startTimeUnix = endTimeMoment.clone().subtract(1, "day").unix() * 1000;
    let endTimeUnix = endTimeMoment.unix() * 1000;
    let $consumptionDataAtStartAndEndtime = [];

    $consumptionDataAtStartAndEndtime.push(
      this.getDataNearbyTimeStamp(emDeviceId, startTimeUnix, positive, negative)
    );
    $consumptionDataAtStartAndEndtime.push(
      this.getDataNearbyTimeStamp(emDeviceId, endTimeUnix, positive, negative)
    );

    let [startTimeData, endTimeData] = await Promise.all(
      $consumptionDataAtStartAndEndtime
    );

    if (startTimeData.problems) return startTimeData;
    if (endTimeData.problems) return endTimeData;

    let consumptionAtStartTime =
      utils.getConsumptionFromDataPacketInUnitPreference(
        startTimeData,
        unitPreference
      );
    let consumptionAtEndTime =
      utils.getConsumptionFromDataPacketInUnitPreference(
        endTimeData,
        unitPreference
      );
    if (consumptionAtStartTime === null) return null;
    if (consumptionAtEndTime === null) return null;

    return consumptionAtEndTime - consumptionAtStartTime;
  },

  /**trh-asset-page.js service function*/

  /**
   * @description getChillerTonnageDelivered will calculate the tonnage delivered by the individual chiller
   * @param { string } siteId
   * @param { string } configuredTrhParameter | tr or tptr
   * @param { datetime } startTime | startTime should be in RFC339 date forma (YYYY-MM-DDTHH:mm:ssZ)
   * @param { datetime } endTime | startTime should be in RFC339 date forma (YYYY-MM-DDTHH:mm:ssZ)
   * @returns { promise } | { siteId, trh }
   */
  getChillerTonnageDelivered: async function (
    siteId,
    chillerAssetId,
    startTime,
    endTime,
    duration
  ) {
    let trhQuery = `
    import "timezone"
    option location = timezone.fixed(offset: {{timezoneOffset}})
    from(bucket: "device_component/autogen")
      |> range(start: {{startTime}}, stop: {{endTime}})
      |> filter(fn: (r) => r["siteId"] == "{{siteId}}")
      |> filter(fn: (r) =>  r["_measurement"] == "components" )
      |> filter(fn: (r) => r["componentId"] == "{{componentId}}")
      |> filter(fn: (r) => r["_field"] == "status" or r["_field"] == "tr" )
      |> pivot(rowKey:["_time"], columnKey: ["_field"], valueColumn: "_value")
      |> filter(fn: (r) => exists r["tr"] and exists r["status"])
      |> filter(fn: (r) => r["status"] == 1.0)
      |> drop(columns: ["componentId", "status"])
      |> aggregateWindow(every: 1h, fn: mean, createEmpty: false,timeSrc:"_start",column:"tr")
      |> rename(columns: {tr: "_value"})
      |> sum()
      |> toInt()
      |> yield(name: "tonnageDelivered")
    `;
    const timezoneOffset = await sails.helpers.getSiteTimezone.with({siteId, timezoneFormat:'utcOffsetInMinute'})
    const result = await influx.runQuery(trhQuery, {
      replacements: {
        siteId: siteId,
        startTime: startTime,
        endTime: endTime,
        componentId: chillerAssetId,
        timezoneOffset
      },
      debug: true,
    });
    let _duration;
    if (duration) {
      _duration = duration;
    } else {
      _duration = `${startTime} - ${endTime}`;
    }
    return {
      duration: _duration,
      data: result,
    };
  },

   /**
   *@description getTotalTonnageDelivered will fetch the total plant room tonnage delivered value in a given time range
   * @param {string} siteId | name of site
   * @param {array} chillerList | list of chiller
   * @param { datetime } startTime | startTime should be in RFC339 date forma (YYYY-MM-DDTHH:mm:ssZ)
   * @param { datetime } endTime | startTime should be in RFC339 date forma (YYYY-MM-DDTHH:mm:ssZ)
   */
   getTotalTonnageDelivered: async function (
    siteId,
    chillerList,
    startTime,
    endTime,
    duration
  ) {
    let trhQuery = `
    import "timezone"
      option location = timezone.fixed(offset: {{timezoneOffset}})
      from(bucket: "device_component/autogen")
        |> range(start: {{startTime}}, stop: {{endTime}})
        |> filter(fn: (r) => r["siteId"] == "{{siteId}}")
        |> filter(fn: (r) =>  r["_measurement"] == "components" )
        |> filter(fn: (r) => {{chillerFilter}})
        |> filter(fn: (r) => r["_field"] == "status" or r["_field"] == "tptr" )
        |> pivot(rowKey:["_time"], columnKey: ["_field"], valueColumn: "_value")
        |> filter(fn: (r) => exists r["tptr"] and exists r["status"])
        |> filter(fn: (r) => r["status"] == 1.0)
        |> drop(columns: ["componentId", "status"])
        |> aggregateWindow(every: 1h, fn: mean, createEmpty: false,timeSrc:"_start",column:"tptr")
        |> rename(columns: {tptr: "_value"})
        |> sum()
        |> toInt()
        |> yield(name: "trh")
    `;

    const chillerFilter = chillerList.map(chiller=>{
      return `r["componentId"] == "${chiller}"`
    }).join(' or ');
    const timezoneOffset = await sails.helpers.getSiteTimezone.with({siteId, timezoneFormat:'utcOffsetInMinute'})
    const result = await influx.runQuery(trhQuery, {
      replacements: {
        siteId: siteId,
        startTime: startTime,
        endTime: endTime,
        chillerFilter: chillerFilter,
        timezoneOffset
      },
      debug: true,
    });
    let _duration;
    if (duration) {
      _duration = duration;
    } else {
      _duration = `${startTime} - ${endTime}`;
    }
    return {
      duration: _duration,
      data: result,
    };
  },

  /**
   * @description getTotalTonnageDeliveredGraph function is generating total tonnage delivered and grouping data at day or hour wise
   * @param { string } siteId
   * @param {array} chillerList | list of chiller
   * @param { datetime } startTime | startTime should be in RFC339 date forma (YYYY-MM-DDTHH:mm:ssZ)
   * @param { datetime } endTime | startTime should be in RFC339 date forma (YYYY-MM-DDTHH:mm:ssZ)
   * @param { string } groupBy | group by can have only hour or day value
   * @returns { promise } | { siteId, trh }
   */
   getTotalTonnageDeliveredGraph: async function (
    siteId,
    chillerList,
    startTime,
    endTime,
    groupBy
  ) {
    let fluxQuery;
    if (groupBy == "hour") {
      fluxQuery = `
      import "timezone"
      option location = timezone.fixed(offset: {{timezoneOffset}})
      from(bucket: "device_component/autogen")
        |> range(start: {{startTime}}, stop: {{endTime}})
        |> filter(fn: (r) => r["siteId"] == "{{siteId}}")
        |> filter(fn: (r) =>  r["_measurement"] == "components" )
        |> filter(fn: (r) => {{chillerFilter}})
        |> filter(fn: (r) => r["_field"] == "status" or r["_field"] == "tptr" )
        |> pivot(rowKey:["_time"], columnKey: ["_field"], valueColumn: "_value")
        |> filter(fn: (r) => exists r["tptr"] and exists r["status"])
        |> filter(fn: (r) => r["status"] == 1.0)
        |> drop(columns: ["componentId", "status"])
        |> aggregateWindow(every: 1h, fn: mean, createEmpty: false,timeSrc:"_start",column:"tptr")
        |> rename(columns: {tptr: "_value"})
        |> toInt()
        |> yield(name: "trh")
      `;
    } else if (groupBy == "day") {
      fluxQuery = `
      import "timezone"
      option location = timezone.fixed(offset: {{timezoneOffset}})
      from(bucket: "device_component/autogen")
        |> range(start: {{startTime}}, stop: {{endTime}})
        |> filter(fn: (r) => r["siteId"] == "{{siteId}}")
        |> filter(fn: (r) =>  r["_measurement"] == "components" )
        |> filter(fn: (r) => {{chillerFilter}})
        |> filter(fn: (r) => r["_field"] == "status" or r["_field"] == "tptr" )
        |> pivot(rowKey:["_time"], columnKey: ["_field"], valueColumn: "_value")
        |> filter(fn: (r) => exists r["tptr"] and exists r["status"])
        |> filter(fn: (r) => r["status"] == 1.0)
        |> drop(columns: ["componentId", "status"])
        |> aggregateWindow(every: 1h, fn: mean, createEmpty: false,timeSrc:"_start",column:"tptr")
        |> aggregateWindow(every: 1d, fn: sum, timeSrc:"_start",createEmpty: false,column:"tptr")
        |> rename(columns: {tptr: "_value"})
        |> toInt()
        |> yield(name: "trh")
      `;
    }
    if (!fluxQuery) throw new Error("INVALID_GROUP_FOR_TRH");
    const chillerFilter = chillerList.map(chiller=>{
      return `r["componentId"] == "${chiller}"`
    }).join(' or ');

    const timezoneOffset = await sails.helpers.getSiteTimezone.with({siteId, timezoneFormat:'utcOffsetInMinute'})
    const result = await influx.runQuery(fluxQuery, {
      replacements: {
        siteId: siteId,
        startTime: startTime,
        endTime: endTime,
        chillerFilter: chillerFilter,
        timezoneOffset
      },
      debug: true,
    });
    return result;
  },

  /**
   * @description getChillerTonnageDeliveredGraph function is generating total tonnage delivered per hour of given chiller asset and grouping data at day or hour wise
   * @param { string } siteId
   * @param {array} chillerList | list of chiller
   * @param { datetime } startTime | startTime should be in RFC339 date forma (YYYY-MM-DDTHH:mm:ssZ)
   * @param { datetime } endTime | startTime should be in RFC339 date forma (YYYY-MM-DDTHH:mm:ssZ)
   * @param { string } groupBy | group by can have only hour or day value
   * @returns { promise } | { array of record}
   */
   getChillerTonnageDeliveredGraph: async function (
    siteId,
    chillerAssetId,
    startTime,
    endTime,
    groupBy
  ) {
    const timezoneOffset = await sails.helpers.getSiteTimezone.with({siteId, timezoneFormat:'utcOffsetInMinute'})
    let fluxQuery;
    if (groupBy == "hour") {
      fluxQuery = `
        import "timezone"
        option location = timezone.fixed(offset: {{timezoneOffset}})
        from(bucket: "device_component/autogen")
          |> range(start: {{startTime}}, stop: {{endTime}})
          |> filter(fn: (r) => r["siteId"] == "{{siteId}}")
          |> filter(fn: (r) =>  r["_measurement"] == "components" )
          |> filter(fn: (r) => r["componentId"] == "{{chillerAssetId}}")
          |> filter(fn: (r) => r["_field"] == "status" or r["_field"] == "tr" )
          |> pivot(rowKey:["_time"], columnKey: ["_field"], valueColumn: "_value")
          |> filter(fn: (r) => exists r["tr"] and exists r["status"])
          |> filter(fn: (r) => r["status"] == 1.0)
          |> aggregateWindow(every: 1h, fn: mean, createEmpty: false,timeSrc:"_start",column:"tr")
          |> rename(columns: {tr: "_value"})
          |> toInt()
          |> yield(name: "trh")
      `;
    } else if (groupBy == "day") {
      fluxQuery = `
      import "timezone"
      option location = timezone.fixed(offset: {{timezoneOffset}})
      from(bucket: "device_component/autogen")
        |> range(start: {{startTime}}, stop: {{endTime}})
        |> filter(fn: (r) => r["siteId"] == "{{siteId}}")
        |> filter(fn: (r) =>  r["_measurement"] == "components" )
        |> filter(fn: (r) => r["componentId"] == "{{chillerAssetId}}")
        |> filter(fn: (r) => r["_field"] == "status" or r["_field"] == "tr" )
        |> pivot(rowKey:["_time"], columnKey: ["_field"], valueColumn: "_value")
        |> filter(fn: (r) => exists r["tr"] and exists r["status"])
        |> filter(fn: (r) => r["status"] == 1.0)
        |> aggregateWindow(every: 1h, fn: mean, createEmpty: false,timeSrc:"_start",column:"tr")
        |> aggregateWindow(every: 1d, fn: sum, timeSrc:"_start",createEmpty: false,column:"tr")
        |> rename(columns: {tr: "_value"})
        |> toInt()
        |> yield(name: "trh")
      `;
    }
    if (!fluxQuery) throw new Error("INVALID_GROUP_FOR_TRH");

    const result = await influx.runQuery(fluxQuery, {
      replacements: {
        siteId: siteId,
        startTime: startTime,
        endTime: endTime,
        chillerAssetId: chillerAssetId,
        timezoneOffset
      },
      debug: true,
    });
    return result;
  },

  /** trh.js helper function */

  /**
   * @description This function will find out the which trh related parameter is configured at chiller. it can be either tr or tptr. It will first check into cache about
   * all configured parameter, if there is miss, it will fetch all parameter, call the set function caching.
   * @param { string } siteId | gknmh
   * @param { array } componentList | ['gknmh_14','gknmh_15']
   * @returns { object } | e.g {gknmh_14:'tr',gknmh_15:'tptr'}
   */
  componentsTrhKeyMap: async function (siteId, componentList) {
    const cachedComponentsDataParameters = {};
    const missedComponentsDataParameters = [];
    for (let componentId of componentList) {
      let _dataParameter = await dataParameter.get(siteId, componentId);
      if (!_.isEmpty(_dataParameter)) {
        cachedComponentsDataParameters[componentId] = new Set(_dataParameter);
      } else {
        missedComponentsDataParameters.push(componentId);
      }
    }
    //if
    if (missedComponentsDataParameters.length) {
      // get data parameter
      for (let componentId of missedComponentsDataParameters) {
        cachedComponentsDataParameters[componentId] = new Set(
          await dataParameter.set(siteId, componentId)
        );
      }
    }

    const map = {};
    componentList.forEach((_component) => {
      if (
        cachedComponentsDataParameters[_component] &&
        cachedComponentsDataParameters[_component].has("tr")
      ) {
        map[_component] = "tr";
      } else if (
        cachedComponentsDataParameters[_component] &&
        cachedComponentsDataParameters[_component].has("tptr")
      ) {
        map[_component] = "tptr";
      } else {
      }
    });
    return map;
  },

  /**
   * @description This function fetching runhouranalysis value for component.
   * @param {object}  startTimestamp - Initial timestamp in YYYY-MM-DDTHH:mm:ssZ format
   *                  endTimestamp -  End timestamp in YYYY-MM-DDTHH:mm:ssZ format
   *                  bucket - site name
   *                  componentId - component id
   *                  groupBy - d for day and h for hour
   *
   *
   * @returns array of records(promise)
   */
  getRunHourAnalysisGraph:async function({startTimestamp,endTimestamp,siteId,componentId,groupBy}){
		let _runHourFluxQuery = `

    import "timezone"
    option location = timezone.fixed(offset: {{timezoneOffset}})
    from(bucket: "device_component/autogen")
      |> range(start: {{startTime}}, stop: {{endTime}})
      |> filter(fn: (r) => r["siteId"] == "{{siteId}}")
      |> filter(fn: (r) =>  r["_measurement"] == "components" )
      |> filter(fn: (r) => r["componentId"] == "{{componentId}}")
      |> filter(fn: (r) => r["_field"] == "status")
      |> filter(fn: (r) => r["_value"] == 1.0 )
      |> pivot(rowKey:["_time"], columnKey: ["_field"], valueColumn: "_value")
      |> aggregateWindow(every: 1{{groupBy}}, fn: sum, createEmpty: false,timeSrc:"_start",column:"status")
      |> map(fn: (r) => ({ r with _value: r.status/60.0 }))
      |> sort(columns: ["_time"], desc: false)
      |> drop(columns: [ "_start","_stop","_measurement","componentId","status"])
      |> yield(name: "result")
			`;
      const timezoneOffset = await sails.helpers.getSiteTimezone.with({siteId, timezoneFormat:'utcOffsetInMinute'})
			const runHourRecord = await influx.runQuery(_runHourFluxQuery, {
					replacements:{
						siteId:siteId,
						startTime:startTimestamp,
						endTime:endTimestamp,
						componentId:componentId,
						groupBy: groupBy,
            timezoneOffset
					},
					debug:true
				});
				const _result = runHourRecord.map(_data=>{
          const {_time,_value} = _data;
					return {
						timestamp:moment(_time).unix()*1000,
						_value:parseFloat(Number(_value).toFixed(2))
					}
				})
				return {
					component:componentId,
					data: _result
				}

	},
  /**
   * @description To get the graphic data of consumption.  currently we using the old REST api of Joulerack repo to calculate the consumption analysis
   * @param {timestamp} startTimestamp | timestamp should be in YYYY-MM-DD HH:mm:ss format
   * @param {timestamp} endTimestamp | timestamp should be in YYYY-MM-DD HH:mm:ss format
   * @param {string} componentList | component list
   * @param {enum} groupBy | currently only two group by supported i.e [h,.d] where h stand for hourly wise and d stands for day wise
   */
  getConsumptionGraph:async function(startTimestamp,endTimestamp,componentList,groupBy){
    const res = await axios.get(`${process.env.JOULETRACK_API}/v1/analytics/paramDiff`, { params: {
      deviceIds: componentList,
      startTime:startTimestamp,
      endTime:endTimestamp,
      params:'kvah',
      groupBy :groupBy
    } });
    return res.data;
  },
  /**
   * @function setUserPreferences
   * @description This function will set the user preferences
   * @param {string} siteId | user id
   * @returns {Array} emList | energy meters list
   * */
   setUserPreferences: async function (userInfo, $isUserPreferencesUpdated) {
    try {
      const userPreferences = await UserSiteMapPublic.findOne(userInfo);
      if (!userPreferences) {
        throw new flaverr('E_NOT_EXIST', new Error('User preference does not exist'))
      }
      let emList;
      if(userPreferences && !userPreferences.consumptionPageEMList || _.isEmpty(userPreferences.consumptionPageEMList)) {
        emList = await DynamoKeyStoreService.findOne({key: `${userInfo.siteId}_em`});
        if (!emList) {
          return;
        }
        emList = emList.list || null;
        $isUserPreferencesUpdated = UserSiteMapPublic.update(userInfo, {
          consumptionPageEMList: emList
        });
        return emList;
      }
      emList = userPreferences && userPreferences.consumptionPageEMList;
      $isUserPreferencesUpdated = userPreferences;
      return emList;
    } catch(e) {
      sails.log.error(`[consumptionPageEMList] Error updating Consumption Page EM list in user preferences for user: ${userInfo.userId} in siteId ${userInfo.siteId}`);
      throw e;
    }
  },
  /**
   * @function getConsumptionDashboard
   * @description This function will get the consumption dashboard data
   * @param {string} siteId | user id
   * @returns {Array} emList | energy meters list
   * */
  getConsumptionDashboard: async function (siteInfo, dashBoardEM) {
    try {

      const {siteId, groupBy} = siteInfo;
      const userPreferredUnit = siteInfo.unitPref.cons || 'kvah';
      const timezoneOffset = await sails.helpers.getSiteTimezone.with({siteId, timezoneFormat:'Z'})

      let start = undefined, end = undefined;
      if (groupBy === 'custom') {
        start = moment(siteInfo.start).utcOffset(timezoneOffset).format('YYYY-MM-DDTHH:mm:00Z')
        end = moment(siteInfo.end).utcOffset(timezoneOffset).format('YYYY-MM-DDTHH:mm:00Z')
      } else  if (groupBy === 'weeks') {
        start = moment().subtract(7, 'days').utcOffset(timezoneOffset).format('YYYY-MM-DDTHH:mm:00Z')
        end = moment().utcOffset(timezoneOffset).format('YYYY-MM-DDTHH:mm:00Z')
      } else if (groupBy === 'hour') {
        start = moment().subtract(60, 'minutes').utcOffset(timezoneOffset).format('YYYY-MM-DDTHH:mm:00Z')
        end = moment().utcOffset(timezoneOffset).format('YYYY-MM-DDTHH:mm:00Z')
      }  else {
        start = moment().subtract(1, groupBy).utcOffset(timezoneOffset).format('YYYY-MM-DDTHH:mm:00Z')
        end = moment().utcOffset(timezoneOffset).format('YYYY-MM-DDTHH:mm:00Z')
      }

      let response = {
        "err": [],
        "data": [],
      };
      dashBoardEM = _.isArray(dashBoardEM) && dashBoardEM || dashBoardEM.values;
      let $calculatedConsumptionForDevices = dashBoardEM.map((devId)=> {
        let searchObjBeg = {
          deviceId: devId,
          startTimestamp: start,
          endTimestamp: end,
          siteId: siteInfo.siteId,
          sortByDesc: false,
        };

        let searchObjEnd = {
          deviceId: devId,
          startTimestamp: start,
          endTimestamp: end,
          siteId: siteInfo.siteId,
          sortByDesc: true,
        };

        return this.calculateConsumptionWithUserPreference(searchObjBeg, searchObjEnd, userPreferredUnit);
      })

      const consumptionDataInArray = await Promise.all($calculatedConsumptionForDevices)
      for (let i = 0; i < consumptionDataInArray.length; i++) {
        if (consumptionDataInArray[i].data) {
          response.data.push(consumptionDataInArray[i].data);
        } else {
          response.err.push(consumptionDataInArray[i].err);
        }
      }
      return response;
    } catch(e) {
      sails.log(e);
      response.data = null;
      response.err = 'Error Fetching Data';
      return response;
    }
  },
  /**
   * @function calculateConsumptionWithUserPreference
   * @description This function will get the consumption dashboard data
   * @param {string} siteId | user id
   * @returns {Array} emList | energy meters list
   */
  calculateConsumptionWithUserPreference: async function (consumptionStartingTimeQueryObj, consumptionEndingTimeQueryObj, userPreferredUnit) {
    try {
      const deviceId = consumptionStartingTimeQueryObj.deviceId;
      let [startTimeDataPacket, endTimeDataPacket] = await Promise.all([
       this.fetchConsumptionData(consumptionStartingTimeQueryObj),
       this.fetchConsumptionData(consumptionEndingTimeQueryObj)
      ]);
      if (_.isEmpty(startTimeDataPacket)) {
        return  {
          err: { deviceId: deviceId, err: 'No Start data'}
        }
      }
      if (_.isEmpty(endTimeDataPacket)) {
        return  {
          err: { deviceId: deviceId, err: 'No End data'}
        }
      }
      let initialConsumption = {}, lastConsumption= {};
      for (const rowData of startTimeDataPacket.data) {
          initialConsumption[rowData._field] = rowData._value;
      }
      for (const rowData of endTimeDataPacket.data) {
          lastConsumption[rowData._field] = rowData._value;
      }

      let startedConsumptionValue = globalHelpers.getConsumptionFromDeviceData(initialConsumption, userPreferredUnit);
      let endedConsumptionValue = globalHelpers.getConsumptionFromDeviceData(lastConsumption, userPreferredUnit);
      let consumption = endedConsumptionValue -  startedConsumptionValue;
      if (isNaN(consumption) || consumption < 0) {
        consumption = 0;
      }
      return {
        data: {
          deviceId: deviceId,
          consumption: globalHelpers.returnFilteredNumber(consumption),
          kw: globalHelpers.returnFilteredNumber(initialConsumption.kw)
        }
      }
    } catch(e) {
      sails.log(e);
      return {
        err: {
          deviceId: consumptionStartingTimeQueryObj.deviceId,
          err: "No Start Data"
        }
      }
    }
  },
  /**
   * @function fetchConsumptionData
   * @description This function will get the consumption dashboard data from influx db
   * @param {string} siteId | user id
   * @returns {Array} emList | energy meters list
   */
   fetchConsumptionData: async function(queryObject) {
    try {
        const query = `from(bucket: "{{bucket}}")
        |> range(start: {{startTime}}, stop: {{endTime}})
        |> filter(fn: (r) => r["_measurement"] == "{{measurement}}")
        |> filter(fn: (r) => r["deviceId"] == "{{deviceId}}")
        |> filter(fn: (r) => r["siteId"] == "{{siteId}}")
        |> filter(fn: (r) => r["_field"] == "corrected_kwh" or r["_field"] == "corrected_ebkwh" or r["_field"] == "kw" or r["_field"] == "corrected_kvah")
        |> sort(columns: ["_time"], desc: {{sortByDesc}})
        |> limit(n:1, offset: 0)
        |> yield(name: "ConsumptionData")
        `;
        const _consumptionRecord = await influxEnterpriseService.runQuery(query, {
          replacements: {
            bucket: BUCKET,
            startTime: queryObject.startTimestamp,
            endTime: queryObject.endTimestamp,
            measurement: MEASUREMENT,
            deviceId: queryObject.deviceId,
            siteId: queryObject.siteId,
            sortByDesc: queryObject.sortByDesc,
          },
          debug: true,
        });
        return {
          deviceId: queryObject.deviceId,
          data: _consumptionRecord
        }
      } catch(e) {
        sails.log('Influx Error: ', e);
        sails.log.error(e)
        throw e;
      }
  },
  getMainEMList : dashboardDataDevice.getMainEMList,
  cachedLastDataTime: dashboardDataDevice.cachedLastDataTime,
  hourlyWiseConsumption: dashboardDataDevice.hourlyWiseConsumption,
  getWeeklyConsumption: dashboardDataDevice.getWeeklyConsumption,
  lastDayConsumption: dashboardDataDevice.lastDayConsumption,
  /**
   * @description taking a list of devices
   * @param {Array} deviceList
   * @return {Object<Array>} {
   *   normalDevice,
   *   expertProDevices
   * }
   */
  getDeviceFieldByType: async function (deviceList, siteId) {
    const EXPERT_PRO_DRIVER_TYPE = 7;
    let siteDevices = await Devices.find({
      siteId // Make global secondary index siteId
    })
    const result  = siteDevices.reduce((acc, d) => {
      if (
        d. driverType &&
        d.driverType == EXPERT_PRO_DRIVER_TYPE &&
        d.siteId === siteId &&
        deviceList.includes(d.deviceId)
        )
       {
        acc.expertPro.devices.push(d.deviceId)
      } else if (
        d.siteId === siteId &&
        deviceList.includes(d.deviceId)
        ) {
        acc.nf29.devices.push(d.deviceId)
      }
      return acc;
    }, {
      nf29: {
        devices: [],
        fields: [
          'corrected_kwh',
          'corrected_kvah'
        ]
      },
      expertPro: {
        devices: [],
        fields: [
        `corrected_kvah`,
        'corrected_ebkwh',
        ]
      }
    })
    return result
  },
  /**
   * this function is fetching the last component data and formatted in a existing format
   * format is {deviceId:<string>,timestamp:<timestamp>,siteId:<string,data:{key1:<string | number | flaot>}}
   * @param deviceId
   * @param {*} startTime
   * @param {*} endTime
   * @param sortInDescending
   * @returns {array}
   */
  //TODO: need to check for device id also
  getDataBetweenTwoTimeStampsFromInfluxdb: async function(deviceId,startTime,endTime,sortInDescending=false){
    const measurement = await this._getMeasurementByDeviceId(deviceId);
    const query = `
      from(bucket: "device_component/autogen")
        |> range(start: {{startTime}}, stop: {{endTime}})
        |> filter(fn: (r) => r["_measurement"] == "{{measurement}}")
        |> filter(fn: (r) => r["componentId"] == "{{deviceId}}" or r["deviceId"] == "{{deviceId}}")
        |> pivot(rowKey:["_time"], columnKey: ["_field"], valueColumn: "_value")
        |> sort(columns: ["_time"], desc: {{sortInDescending}})
        |> yield(name: "deviceData")
    `;

    // TODO: To add a flag to query in desc order.
    const dataPoints = await influx.runQuery(query, {
      replacements: {
        measurement,
        startTime,
        endTime,
        deviceId,
        sortInDescending
      },
      debug: true,
    });


    return dataPoints.map(record => {
      const {
        siteId,
        _time: timestamp
      } = record
      delete record._measurement;
      delete record._start;
      delete record._stop;
      delete record.field1;
      delete record.result;
      delete record.deviceId;
      delete record.componentId;
      delete record.siteId;
      delete record._time;
      delete record.table;

      Object.keys(record)
        .forEach(key => {
          record[key] = isNaN(record[key]) ? record.key : Number(record[key]);
        })
      return {
        deviceId,
        siteId,
        timestamp: moment(timestamp)
          .format('YYYY-MM-DD HH:mm:ss'),
        data: record
      }
    })
  },
  /**
   * This function is identifying the measurement to run query on the influxdb. dynamodb's datadevice table contains component and device
   * data together, but influxdb store component data into components measurement and device data into device table.
   *
   * @param {*} deviceId
   * @returns
   */
  _getMeasurementByDeviceId: async function(deviceId){

    //it can be potentially component id because component id format is `${siteId}_${incremental}`
    if(isNaN(deviceId)){
      return "components"
    }
    return "device";

    /**
     * Below commented code can look into database to really verify the type of device.
     * I have commented for future use and avoiding unnecessary call on component and device table dynamodb
     *
     */
    // const [isComponent, isDevice] = await Promise.all([Components.findOne({deviceId}), Devices.findOne({deviceId})]);
    // if(_.isEmpty(isComponent) && _.isEmpty(isDevice)) return null;
    // if(!_.isEmpty(isComponent)) measurement="components";
    // if(!_.isEmpty(isDevice)) measurement="device";
    // return measurement

  },

  getComponentRecentDataByParams: async function ({ siteId, componentId, paramList, lastValidDays = 30 }) {
    if (_.isEmpty(paramList)){
      return { recentDataList: [] }
    }
    const utcOffset = await sails.helpers.getSiteTimezone.with({ siteId, timezoneFormat: 'Z' })
    const startTime = moment().utcOffset(utcOffset).subtract(lastValidDays, 'days').format('YYYY-MM-DDTHH:mm:00Z');
    const endTime = moment().utcOffset(utcOffset).format('YYYY-MM-DDTHH:mm:00Z');

    const componentData = (await this.fetchLatestComponentDataInRange(siteId, componentId, startTime, endTime)) || {}
    delete componentData.runminutes;
    const lastPacketTime = componentData.time && moment(Number.parseInt(componentData.time, 10) / 1000000).utcOffset(utcOffset).format("YYYY-MM-DD HH:mm:ss");
    const response = paramList
      .filter(param => param in componentData)
      .map(param => ({
        param,
        value: componentData[param],
        timestamp: lastPacketTime
      }))

    if (paramList.indexOf("runminutes") > -1) {
      calculatedRunminutes = await this.calculateTotalRunMinutesInRange(siteId, componentId, startTime, endTime)
      response.push({
        param: "runminutes",
        value: Number.parseInt(calculatedRunminutes,10),

        timestamp: moment(endTime).format('YYYY-MM-DD HH:mm:ss')
      })
    }
    return {
      recentDataList: response
    }

  },
  calculateTotalRunMinutesInRange: async function (siteId, componentId, startTime, endTime) {
    const query = `
      SELECT SUM("status") AS "runminutes"
      FROM "device_component"."autogen"."components"
      WHERE
          "siteId" = '${siteId}' AND
          "componentId" = '${componentId}' AND
          "status" = 1 AND
          time >= '${startTime}' AND
          time < '${endTime}'
      GROUP BY "componentId"
    `;

    try {
      const rawResult = await influxEnterpriseService.runInfluxQLQuery(query);

      if (_.isEmpty(rawResult)) {
        return 0;
      }

      const [row] = rawResult;
      return row?.runminutes ?? 0;
    } catch (error) {
      sails.log.error('Error fetching run minutes:', error);
      throw error;
    }
  },
  fetchLatestComponentDataInRange: async function (siteId, componentId, startTime, endTime) {
    const query = `
          SELECT *
      FROM "device_component"."autogen"."components"
      WHERE
          "siteId" = '${siteId}' AND
          "componentId" = '${componentId}' AND
          time >= '${startTime}' AND
          time < '${endTime}'
      ORDER BY time DESC
      LIMIT 1
    `;
    const rawResult = await influxEnterpriseService.runInfluxQLQuery(query);
    if (_.isEmpty(rawResult)) {
      return {};
    }
    return rawResult[0];
  },
  getAssetRunMinuteTableData: async function(siteId, componentId) {
    const timezoneOffset =  await sails.helpers.getSiteTimezone.with({siteId, timezoneFormat:'utcOffsetInMinute'})
    const currentTime =  moment().utcOffset(timezoneOffset);

    let dayStartTime = currentTime.clone().startOf("day");
    let weekStartTime = currentTime.clone().startOf("week");
    let monthStartTime = currentTime.clone().startOf("month");

    let totalMonthFromDayStartTime = currentTime.diff(dayStartTime, "hours");
    let totalWeekFromWeekStartTime = currentTime.diff(weekStartTime, "hours");
    let totalHourFromMonthStartTime = currentTime.diff(monthStartTime, "hours");

    const endTime = currentTime.startOf('hours').format('YYYY-MM-DDTHH:mm:ssZ');
    const [
      totalDayRunningHour,
      totalWeekRunningHour,
      totalMonthRunningHour
    ] = await Promise.all([
      this.calculateTotalRunMinutesInRange(siteId, componentId, dayStartTime.format('YYYY-MM-DDTHH:mm:ssZ'), endTime),
      this.calculateTotalRunMinutesInRange(siteId, componentId, weekStartTime.format('YYYY-MM-DDTHH:mm:ssZ'), endTime),
      this.calculateTotalRunMinutesInRange(siteId, componentId, monthStartTime.format('YYYY-MM-DDTHH:mm:ssZ'), endTime)
    ]);

    const actualDayRunHour = Math.round(totalDayRunningHour/60);
    const actualWeekRunHour =  Math.round(totalWeekRunningHour/60);
    const actualMonthRunHour =  Math.round(totalMonthRunningHour/60) ;

    const currentTimeInISOFormat = currentTime.format('YYYY-MM-DDTHH:mm:00Z');
    const response = {
      "todayRunningAnalysis": {
        "startTimestamp": dayStartTime.format('YYYY-MM-DDTHH:mm:00Z'),
        "endTimeStamp": currentTimeInISOFormat,
        "actualRunningHour": actualDayRunHour,
        "totalHours": totalMonthFromDayStartTime
      },
      "currentWeeklyRunningAnalysis": {
        "startTimestamp": weekStartTime.format('YYYY-MM-DDTHH:mm:00Z'),
        "endTimeStamp": currentTimeInISOFormat,
        "actualRunningHour":actualWeekRunHour,
        "totalHours":totalWeekFromWeekStartTime
      },
      "currentMonthRunningAnalysis": {
        "startTimestamp": monthStartTime.format('YYYY-MM-DDTHH:mm:00Z'),
        "endTimeStamp":currentTimeInISOFormat,
        "actualRunningHour": actualMonthRunHour,
        "totalHours": totalHourFromMonthStartTime
      }
    }

    if (
      actualDayRunHour > totalMonthFromDayStartTime ||
      actualWeekRunHour > totalWeekFromWeekStartTime ||
      actualMonthRunHour > totalHourFromMonthStartTime
    ) {
      const errMessage = `[fn: getAssetRunMinuteTableData] The actual run minutes exceed the total for site :${siteId} and component: ${componentId}.`;
      sentry.captureException(new Error(errMessage), { extra: { response } })
    }

    return response
  },
  getCachedRecentData: async function(componentId) {
    const CACHE_kEY = `asset:${componentId}:recent-data`
    const record = await cacheService.get(CACHE_kEY);
    return record ? JSON.parse(record): null;
  },
  getDeviceLastDataPoint: async function (siteId,deviceId) {
    try {
      if (_.isEmpty(deviceId) || _.isEmpty(siteId)) {
        throw new Error('Invalid deviceId or siteId')
      };
      const cachedData = await this.getCachedRecentData(deviceId);
      if(cachedData) return cachedData;
      const query = `
                  SELECT *
                  FROM "device_component"."autogen"."device"
                  WHERE "deviceId" = '${deviceId}' AND "siteId" = '${siteId}'
                  ORDER BY time DESC
                  LIMIT 1
                  `;
      const $rawResult = influxEnterpriseService.runInfluxQLQuery(query);
      const $timezone =  sails.helpers.getSiteTimezone.with({ siteId });
      const [recentData, timezone] = await Promise.all([$rawResult,$timezone])
      if (_.isEmpty(recentData)) {
        return null;
      }
      const _response = {
        deviceId: recentData[0].deviceId,
        siteId: recentData[0].siteId,
        timestamp: null,
        data: {}
      };
      for(let [key,value] of Object.entries(recentData[0])) {
        if(key === "time") {
          _response['timestamp'] = momentTz(parseInt(value, 10) / 1000000).utcOffset(timezone).format('YYYY-MM-DDTHH:mm:ssZ');
        } else if(value != '' && key != 'name' && key != 'tag' && key!= 'deviceId' && key!='siteId') {
          _response.data[key] = parseFloat(value);
        }
      }
      const cacheKey = `asset:${deviceId}:recent-data`;
      await cacheService.set(cacheKey, JSON.stringify(_response));
      await cacheService.expire(cacheKey,sails.config.custom.LAST_DATA_POINT_TTL);
      return _response;
    } catch (error) {
      sails.log.error(`['[LAST_DATA_POINT] getDeviceLastDataPoint']`, error);
      throw error;
    }
  },
  getComponentLastDataPoint: async function(siteId, componentId) {
    try {
      if (_.isEmpty(componentId) || _.isEmpty(siteId)) {
        throw new Error('Invalid componentId or siteId')
      };
      const cachedData = await this.getCachedRecentData(componentId);
      if (cachedData) return cachedData;
      const query = `
                  SELECT *
                  FROM "device_component"."autogen"."components"
                  WHERE "componentId" = '${componentId}' AND "siteId" = '${siteId}'
                  ORDER BY time DESC
                  LIMIT 1
                  `;
      const $rawResult = influxEnterpriseService.runInfluxQLQuery(query);
      const $timezone = sails.helpers.getSiteTimezone.with({ siteId });
      const [recentData, timezone] = await Promise.all([$rawResult, $timezone])
      if (_.isEmpty(recentData)) {
        return null;
      }
      const _response = {
        deviceId: recentData[0].componentId,
        siteId: recentData[0].siteId,
        timestamp: null,
        data: {}
      };
      for (let [key, value] of Object.entries(recentData[0])) {
        if (key === "time") {
          _response['timestamp'] = momentTz(parseInt(value, 10) / 1000000).utcOffset(timezone).format('YYYY-MM-DDTHH:mm:ssZ');
        } else if (value != '' && key != 'name' && key != 'tag' && key != 'componentId' && key != 'siteId') {
          _response.data[key] = parseFloat(value);
        }
      }
      const cacheKey = `asset:${componentId}:recent-data`;
      await cacheService.set(cacheKey, JSON.stringify(_response));
      await cacheService.expire(cacheKey,sails.config.custom.LAST_DATA_POINT_TTL);
      return _response;
    } catch (error) {
      sails.log.error(`[LAST_DATA_POINT] getComponentLastDataPoint`, error);
      throw error;
    }
  },
  getBatchComponentLastDataPoint: async function (siteId, componentIds) {
    if (_.isEmpty(componentIds)) return [];
    sails.log.info(`Fetching from Influx - siteId: ${siteId}, componentIds: ${componentIds.join(',')}`);
    let componentFilter = componentIds.map(id => `"componentId" = '${id}'`).join(' OR ');
    const query = `
        SELECT *
        FROM "device_component"."autogen"."components"
        WHERE "siteId" = '${siteId}' AND (${componentFilter})
        AND time >= now() - 1h
        GROUP BY "componentId"
        ORDER BY time DESC
        LIMIT 1
    `;
    const results = await influxEnterpriseService.runInfluxQLQuery(query);
    if (_.isEmpty(results)) return [];
    const timezone = await sails.helpers.getSiteTimezone.with({ siteId });
    const formattedResults = results.reduce((acc, item) => {
      const componentIdMatch = item.tags.match(/componentId=([^,]+)/);
      if (componentIdMatch) {
        const componentId = componentIdMatch[1];
        const timestamp = moment(parseInt(item.time, 10) / 1000000).utcOffset(timezone, true).format('YYYY-MM-DDTHH:mm:ssZ');
        const formattedItem = { deviceId: componentId, timestamp };
        Object.keys(item).forEach(key => {
          if (item[key] !== "" && item[key] !== null && !['name','tags','time'].includes(key)) {
            const cleanKey = key.startsWith("last_") ? key.replace("last_", "") : key;
            const value = isNaN(Number(item[key])) ? item[key] : parseFloat(item[key])
            formattedItem[cleanKey] = value;
          }
        });
        acc.push(formattedItem);
      }
      return acc;
    }, []);
    return formattedResults;
  },
  getBatchDeviceLastDataPoint: async function (siteId, deviceIds) {
    if (_.isEmpty(deviceIds)) return [];
    sails.log.info(`Fetching from Influx - siteId: ${siteId}, deviceIds: ${deviceIds.join(',')}`);

    let deviceFilter = deviceIds.map(id => `"deviceId" = '${id}'`).join(' OR ');
    const query = `
    SELECT *
    FROM "device_component"."autogen"."device"
    WHERE "siteId" = '${siteId}' AND (${deviceFilter})
    AND time >= now() - 1h
    GROUP BY "deviceId"
    ORDER BY time DESC
    LIMIT 1
  `;

    const results = await influxEnterpriseService.runInfluxQLQuery(query);
    if (_.isEmpty(results)) return [];
    const timezone = await sails.helpers.getSiteTimezone.with({ siteId });
    const formattedResults = results.reduce((acc, item) => {
      const deviceIdMatch = item.tags.match(/deviceId=([^,]+)/);
      if (deviceIdMatch) {
        const deviceId = deviceIdMatch[1];
        const timestamp = moment(parseInt(item.time, 10) / 1000000).utcOffset(timezone).format('YYYY-MM-DDTHH:mm:ssZ');
        const formattedItem = { deviceId, timestamp };
        Object.keys(item).forEach(key => {
          if (item[key] !== "" && item[key] !== null && !['name','tags','time','siteId'].includes(key)) {
            const cleanKey = key.startsWith("last_") ? key.replace("last_", "") : key;
            const value = isNaN(Number(item[key])) ? item[key] : parseFloat(item[key])
            formattedItem[cleanKey] = value;
          }
        });
        acc.push(formattedItem);
      }
      return acc;
    }, []);
    return formattedResults;
  },
  getBatchCachedRecentData: async function (componentIds) {
    const cachedComponentsData = {};
    const cacheKeys = componentIds.map(id => `asset:${id}:recent-data`);
    try {
      const records = await cacheService.mget(cacheKeys);
      if (_.isEmpty(records)) return cachedComponentsData;
      records.forEach((record, idx) => {
        if (_.isEmpty(record)) return;
        try {
          const parsedRecord = JSON.parse(record)
          if (_.isEmpty(parsedRecord.deviceId) || _.isEmpty(parsedRecord.data)) return;
          const formattedTimestamp = moment(parsedRecord.timestamp).format('YYYY-MM-DDTHH:mm:ssZ');
          cachedComponentsData[parsedRecord.deviceId] = { deviceId: parsedRecord.deviceId, timestamp: formattedTimestamp, ...parsedRecord.data };
        } catch (error) {
          sails.log.error(`Error parsing cached recent data for component ID: ${componentIds[idx]} message = "${error.message}"`, error)
        }
      })
    } catch (error) {
      sails.log.error(`Error fetching from cache: message = "${error.message}"`, error);
    }
    return cachedComponentsData;
  },
  getAssetRecentData: async ({ siteId, deviceId, startTimestamp, endTimestamp }) => {
    const isDevice = !isNaN(deviceId);
    const identifierKey = isDevice ? "deviceId" : "componentId";
    const measurement = isDevice ? "device" : "components";
    const query = `
      SELECT *
      FROM "device_component"."autogen"."${measurement}"
      WHERE "${identifierKey}" = '${deviceId}' AND "siteId" = '${siteId}' AND time >= '${startTimestamp}' AND time < '${endTimestamp}'
      ORDER BY time DESC
    `;
    const results = await influxEnterpriseService.runInfluxQLQuery(query);
    if (_.isEmpty(results)) return [];
    const formattedResults = results.map((data) => {
      const formattedData = {};
      for (const [k, v] of Object.entries(data)) {
        if (k == "time") formattedData.time = momentTz(parseInt(v) / 1000000).format("YYYY-MM-DDTHH:mm:ssZ");
        else if (
          v != "" &&
          v != null &&
          v != undefined &&
          !["name", "componentId", "siteId"].includes(k)
        )
          formattedData[k] = isNaN(Number(v)) ? v : Number(v);
      }
      return formattedData;
    });
    return formattedResults;
  },
  generateRunHourAnalysis: async function (siteId, componentIds, startTime, endTime, groupBy) {
    const timezone = await sails.helpers.getSiteTimezone.with({ siteId, timezoneFormat: 'tz' });
    const componentFilter = componentIds.map(id => `"componentId" = '${id}'`).join(' OR ');
    const query = `
    SELECT SUM("status") / 60.0 AS "value"
    FROM "device_component"."autogen"."components"
    WHERE "siteId" = '${siteId}' AND (${componentFilter}) AND "status" = 1 AND time >= '${startTime}' AND time < '${endTime}'
    GROUP BY time(1${groupBy}), "componentId"
    ORDER BY time ASC tz('${timezone}')
  `;
    const results = await influxEnterpriseService.runInfluxQLQuery(query);
    if (_.isEmpty(results)) return [];
    const componentResultMap = results.reduce((acc, item) => {
      const componentIdMatch = item.tags.match(/componentId=([^,]+)/);
      if (componentIdMatch) {
        const componentId = componentIdMatch[1];
        if(!acc[componentId]) acc[componentId] = [];
        acc[componentId].push([ item.time / 1e6, parseFloat(parseFloat(item.value).toFixed(2)) ]);
      }
      return acc;
    },{})
    return componentResultMap;
  },
  generateConsumptionAnalysis: async function (siteId, componentIds, startTime, endTime, groupBy) {
    const timezone = await sails.helpers.getSiteTimezone.with({ siteId, timezoneFormat: 'tz' });
    const componentFilter = componentIds.map(id => `"componentId" = '${id}'`).join(' OR ');
    const query = `
    SELECT FIRST("kvah") AS "start_value", LAST("kvah") AS "end_value"
    FROM "device_component"."autogen"."components"
    WHERE "siteId" = '${siteId}' AND (${componentFilter}) AND time >= '${startTime}' AND time < '${endTime}'
    GROUP BY time(1${groupBy}), "componentId"
    ORDER BY time ASC tz('${timezone}')
  `;
    const results = await influxEnterpriseService.runInfluxQLQuery(query);
    if (_.isEmpty(results)) return [];
    const componentResultMap = results.reduce((acc, item) => {
      const componentIdMatch = item.tags.match(/componentId=([^,]+)/);
      if (componentIdMatch) {
        const componentId = componentIdMatch[1];
        if (!acc[componentId]) acc[componentId] = [];
        const startKvah = parseFloat(item.start_value);
        const endKvah = parseFloat(item.end_value);
        const consumption = endKvah - startKvah;
        acc[componentId].push([ item.time / 1e6, consumption ]);
      }
      return acc;
    }, {});
    return componentResultMap;
  },
  generateDevicesAnalyticsData: async function (siteId, deviceIds, startTime, endTime, groupBy, params) {
    const timezone = await sails.helpers.getSiteTimezone.with({ siteId, timezoneFormat: 'tz' });
    const paramSelection = params.map(param => `mean("${param}") AS "${param}"`).join(", ");
    const deviceFilter = deviceIds.map(id => `"deviceId" = '${id}'`).join(' OR ');
    const query = `
      SELECT ${paramSelection}
      FROM "device_component"."autogen"."device"
      WHERE "siteId" = '${siteId}' AND (${deviceFilter}) AND time >= '${startTime}' AND time < '${endTime}'
      GROUP BY time(1${groupBy}), "deviceId"
      ORDER BY time ASC tz('${timezone}')
    `;
    const queryResults = await influxEnterpriseService.runInfluxQLQuery(query);
    if (_.isEmpty(queryResults)) return [];
    const devicesMap = {};
    queryResults.forEach(item => {
      const deviceId = item.tags.match(/deviceId=([^,]+)/)[1];
      const timestamp = parseInt(item.time) / 1e6;
      if (!devicesMap[deviceId]) {
        devicesMap[deviceId] = {
          deviceId,
          plot: {}
        };
        params.forEach(param => {
          devicesMap[deviceId].plot[param] = [];
        });
      }
      params.forEach(param => {
        const paramValue = parseFloat(parseFloat(item[param]).toFixed(2));
        if (!isNaN(paramValue)) {
          devicesMap[deviceId].plot[param].push([timestamp, paramValue]);
        }
      });
    });
    return Object.values(devicesMap);
  },
  getBuildingLoadByDay(data, mainMeterCount) {
    try {
      const processedData = {};
      const dataByTime = {};
      data.forEach(item => {
        const timestamp = parseInt(item.time);
        const hour = timestamp / 1000000;
        const totalKw = parseFloat(item.total_kw) || 0;
        const countKw = parseInt(item.count_kw) || 1;
        const avgKw = totalKw / countKw;
        if (!dataByTime[hour]) {
          dataByTime[hour] = [];
        }
        dataByTime[hour].push(avgKw);
      });
      Object.keys(dataByTime).forEach(hour => {
        if (dataByTime[hour].length === mainMeterCount) {
          const totalKw = dataByTime[hour].reduce((sum, kw) => sum + kw, 0);
          const timestamp = parseInt(hour);
          const day = moment(timestamp).startOf("day").format("YYYY-MM-DD HH:mm");
          if (!processedData[day]) {
            processedData[day] = [];
          }
          processedData[day].push([timestamp, Math.round(totalKw * 100) / 100]);
        }
      });
      Object.keys(processedData).forEach(date => {
        processedData[date].sort((a, b) => a[0] - b[0]);
      });
      return processedData;
    } catch (error) {
      sails.log.error('Error in getBuildingLoadByDay:', error.message);
      throw flaverr({
        code: 'E_FETCH_CONSUMPTION',
        message: `Error in getBuildingLoadByDay: ${error.message}`,
      });
    }
  },
  async fetchBuildingLoadByHour(siteId, deviceIds, timeRanges) {
    const timezone = await sails.helpers.getSiteTimezone.with({ siteId, timezoneFormat: 'tz' });
    const queries = timeRanges.flatMap(range =>
      deviceIds.map(deviceId => `
      SELECT
        SUM("kw") AS "total_kw",
        COUNT("kw") AS "count_kw"
      FROM "device_component"."autogen"."device"
      WHERE "siteId" = '${siteId}'
        AND "deviceId" = '${deviceId}'
        AND time >= '${range.startTime}'
        AND time < '${range.endTime}'
      GROUP BY time(1h) tz('${timezone}')
    `)
    );
    const results = await Promise.all(queries.map(async query => {
      try {
        return await influxEnterpriseService.runInfluxQLQuery(query);
      } catch (error) {
        console.error('Query execution error:', error.message, 'Query:', query);
        return [];
      }
    }));
    return results.flat();
  },
  generateAhuOperationalPattern: async (siteId, componentIds, startTime, endTime, param) => {
    const res = {
      best: [],
      worst: [],
      notWorking: []
    }
    if(_.isEmpty(componentIds)) return res;
    const timezone = await sails.helpers.getSiteTimezone.with({ siteId, timezoneFormat: 'tz' });
    const componentFilter = componentIds.map(id => `"componentId" = '${id}'`).join(' OR ');
    const query = `
      SELECT ${param}
      FROM "device_component"."autogen"."components"
      WHERE "siteId" = '${siteId}' AND (${componentFilter}) AND time >= '${startTime}' AND time < '${endTime}' AND status = 1
      GROUP BY "componentId"
      ORDER BY time ASC tz('${timezone}')
    `;
    const queryResults = await influxEnterpriseService.runInfluxQLQuery(query);
    if (_.isEmpty(queryResults)) {
      res.notWorking = componentIds.map(id => ({ [id]: "NA" }));
      return res;
    }
    const componentParamMap = {};
    queryResults.forEach(item => {
      const componentId = item.tags.match(/componentId=([^,]+)/)[1];
      const timestamp = parseInt(item.time) / 1e6;
      if (!componentParamMap[componentId]) {
        componentParamMap[componentId] = {
          values: [],
          timestamps: []
        };
      }
      const defaultUse = param == 'outputfrequency' ? 50 : 100;
      let paramValue = parseFloat(parseFloat(item[param]).toFixed(2))
      if (isNaN(paramValue)) paramValue = defaultUse;
      componentParamMap[componentId].values.push(paramValue);
      componentParamMap[componentId].timestamps.push(timestamp);
    });
    const componentsWithData = [];
    componentIds.forEach(id => {
      if (!componentParamMap[id]) {
        res.notWorking.push({ [id]: "NA" });
      } else {
        const data = componentParamMap[id];
        const stdDev = globalHelpers.standardDeviation(data.values);
        componentsWithData.push({ componentId: id, stdDev, data });
      }
    });
    componentsWithData.sort((a, b) => b.stdDev - a.stdDev);
    const totalComponents = componentsWithData.length;
    let bestComponents, worstComponents;
    if (totalComponents < 10) {
      const halfIndex = Math.ceil(totalComponents / 2);
      bestComponents = componentsWithData.slice(0, halfIndex);
      worstComponents = componentsWithData.slice(halfIndex).reverse();
    } else {
      bestComponents = componentsWithData.slice(0, 5);
      worstComponents = componentsWithData.slice(-5).reverse();
    }
    bestComponents.forEach(({ componentId, data }) => {
      res.best.push({ [componentId]: [data.timestamps.map((ts, i) => [ts, data.values[i]])] });
    });
    worstComponents.forEach(({ componentId, data }) => {
      res.worst.push({ [componentId]: [data.timestamps.map((ts, i) => [ts, data.values[i]])] });
    });
    return res;
  },
  generateSiteConsumptionData: async function (siteId, deviceIds, startTime, endTime, unit, groupBy) {
    try {
      if(!['kwh', 'kvah'].includes(unit)) {
        throw flaverr({
          code: 'E_INVALID_UNIT',
          message: "Invalid unit specified. Use 'kwh' or 'kvah'.",
          status: 400,
        })
      }
      unit = `corrected_${unit}`;
      const deviceFilter = deviceIds.map(id => `"deviceId" = '${id}'`).join(' OR ');
      const timezone = await sails.helpers.getSiteTimezone.with({ siteId, timezoneFormat: 'tz' });
      let adjustedGroupBy;
      if (groupBy === 'month') {
        adjustedGroupBy = '1d'; // will do manual monthly aggregation
      } else if (groupBy === 'hour') {
        adjustedGroupBy = '1h';
      } else if (groupBy === 'day') {
        adjustedGroupBy = '1d';
      } else {
        throw flaverr({
          code: 'E_INVALID_GROUPBY',
          message: "Invalid groupBy value. Allowed values are 'hour', 'day', 'month'.",
          status: 400,
        });
      }

      const query = `
      SELECT FIRST("${unit}") AS "start_value", LAST("${unit}") AS "end_value"
      FROM "device_component"."autogen"."device"
      WHERE "siteId" = '${siteId}' AND (${deviceFilter}) AND time >= '${startTime}' AND time < '${endTime}'
      GROUP BY time(${adjustedGroupBy}), "deviceId"
      ORDER BY time ASC tz('${timezone}')
    `;

      const results = await influxEnterpriseService.runInfluxQLQuery(query);
      const formattedResponse = {
        data: {},
        columns: []
      };
      let currentTime, endMoment;
      if (groupBy === 'month') {
        currentTime = moment(startTime).startOf('month');
        endMoment = moment(endTime).startOf('month');
      } else {
        currentTime = moment(startTime).startOf(groupBy === 'hour' ? 'hour' : 'day');
        endMoment = moment(endTime).startOf(groupBy === 'hour' ? 'hour' : 'day');
      }
      while (currentTime.isBefore(endMoment) || currentTime.isSame(endMoment)) {
        formattedResponse.columns.push(currentTime.format('YYYY-MM-DD HH:mm:ss'));
        if (groupBy === 'hour') {
          currentTime.add(1, 'hours');
        } else if (groupBy === 'day') {
          currentTime.add(1, 'days');
        } else if (groupBy === 'month') {
          currentTime.add(1, 'months');
        }
      }
      deviceIds.forEach(deviceId => {
        formattedResponse.data[deviceId] = formattedResponse.columns.map(timestamp => [timestamp, null]);
      });
      const deviceData = {};
      results.forEach(item => {
        const deviceId = item.tags.match(/deviceId=([^,]+)/)[1];
        const startValue = parseFloat(item.start_value);
        const endValue = parseFloat(item.end_value);
        const consumption = parseFloat((endValue - startValue).toFixed(2));
        const timestamp = moment(parseInt(item.time) / 1e6).format('YYYY-MM-DD HH:mm:ss');
        if (!deviceData[deviceId]) {
          deviceData[deviceId] = [];
        }
        deviceData[deviceId].push({ timestamp, consumption });
      });

      if (groupBy === 'month') {
        Object.keys(deviceData).forEach(deviceId => {
          const monthlyData = {};
          deviceData[deviceId].forEach(({ timestamp, consumption }) => {
            const monthKey = moment(timestamp).startOf('month').format('YYYY-MM');
            if (!monthlyData[monthKey]) {
              monthlyData[monthKey] = 0;
            }
            monthlyData[monthKey] += consumption;
          });

          formattedResponse.data[deviceId] = formattedResponse.columns.map(timestamp => {
            const monthKey = moment(timestamp).startOf('month').format('YYYY-MM');
            const totalConsumption = monthlyData[monthKey] || null;
            return [timestamp, totalConsumption];
          });
        });
      } else {
        Object.keys(deviceData).forEach(deviceId => {
          const dataMap = deviceData[deviceId].reduce((map, { timestamp, consumption }) => {
            map[timestamp] = consumption;
            return map;
          }, {});
          formattedResponse.data[deviceId] = formattedResponse.columns.map(timestamp => {
            return [timestamp, dataMap[timestamp] || null];
          });
        });
      }
      return formattedResponse;
    } catch (error) {
      sails.log.error(`Error fetching site consumption data: ${error.message}`, error);
      throw error;
    }
  },
};

dataDeviceService.getBuildingLoadByDay = dataDeviceService.getBuildingLoadByDay.bind(dataDeviceService);
dataDeviceService.getComponentLastDataPoint = dataDeviceService.getComponentLastDataPoint.bind(dataDeviceService);
module.exports = dataDeviceService;
