/**
 * alertIncidentHistory.service.js
 * @description :: Service layer with business logic for alert incident history
 */

const alertIncidentHistoryService = require('./alertIncidentHistory.private');
const moment = require('moment-timezone');
const flaverr = require('flaverr');
const RedisClient = require('../../cache/cache.public');

/**
 * Extract start_time and end_time from cron expression
 * @param {string} cronExpression - Cron expression (e.g., "* 9-17 * * *")
 * @returns {Array} Array of time range objects with start_time and end_time
 */
function extractTimeRangesFromCron(cronExpression) {
  if (!cronExpression || typeof cronExpression !== "string") {
    return [];
  }

  // Handle multi-line cron expressions (remove quotes and split by newlines)
  const cleanCron = cronExpression.replace(/['"]/g, '').trim();
  const cronLines = cleanCron.split('\n').filter(line => line.trim());

  // If multiple cron lines, process each and combine results
  if (cronLines.length > 1) {
    const allTimeRanges = [];
    for (const cronLine of cronLines) {
      const lineRanges = extractTimeRangesFromCron(cronLine.trim());
      allTimeRanges.push(...lineRanges);
    }
    return allTimeRanges;
  }

  const parts = cleanCron.trim().split(/\s+/);
  if (parts.length !== 5) {
    return [];
  }

  const [minute, hour] = parts;
  const timeRanges = [];

  try {
    // Parse hour field to get all hours (0-23)
    const hours = parseTimeField(hour, 23);
    if (hours.length === 0) {
      return [];
    }

    // Parse minute field to get minute ranges (0-59)
    const minutes = parseTimeField(minute, 59);

    // Group consecutive hours into ranges
    const hourRanges = groupConsecutiveNumbers(hours);

    for (const hourRange of hourRanges) {
      let startTime, endTime;

      if (minute === "*" || minute === "0-59") {
        // Full hour ranges
        startTime = `${hourRange.start.toString().padStart(2, "0")}:00:00`;
        endTime = `${hourRange.end.toString().padStart(2, "0")}:59:59`;
      } else if (minutes.length > 0) {
        // Specific minute ranges - use the first and last minute
        const minMinute = Math.min(...minutes);
        const maxMinute = Math.max(...minutes);

        // Validate minute values
        if (minMinute >= 0 && minMinute <= 59 && maxMinute >= 0 && maxMinute <= 59) {
          startTime = `${hourRange.start.toString().padStart(2, "0")}:${minMinute.toString().padStart(2, "0")}:00`;
          endTime = `${hourRange.end.toString().padStart(2, "0")}:${maxMinute.toString().padStart(2, "0")}:59`;
        } else {
          // Invalid minute range, fallback to full hour
          startTime = `${hourRange.start.toString().padStart(2, "0")}:00:00`;
          endTime = `${hourRange.end.toString().padStart(2, "0")}:59:59`;
        }
      } else {
        // No valid minutes parsed, fallback to full hour
        startTime = `${hourRange.start.toString().padStart(2, "0")}:00:00`;
        endTime = `${hourRange.end.toString().padStart(2, "0")}:59:59`;
      }

      timeRanges.push({ start_time: startTime, end_time: endTime });
    }
  } catch (error) {
    sails.log.warn("Error parsing cron expression:", cronExpression, error);
    return [];
  }

  return timeRanges;
}

/**
 * Parse a cron time field (minute or hour) into an array of numbers
 * @param {string} field - Cron field (e.g., "0-5", "1,3,5", "*")
 * @param {number} maxValue - Maximum value for the field (23 for hours, 59 for minutes)
 * @returns {Array} Array of numbers
 */
function parseTimeField(field, maxValue = 23) {
  if (!field || field === "*") {
    // For "*", return all possible values
    return Array.from({ length: maxValue + 1 }, (_, i) => i);
  }

  const numbers = [];
  const parts = field.split(",");

  for (const part of parts) {
    if (part.includes("-")) {
      // Range like "0-5" or "12-4" (cross-midnight)
      const [start, end] = part.split("-").map(n => parseInt(n.trim()));
      if (!isNaN(start) && !isNaN(end)) {
        if (start <= end) {
          // Normal range
          for (let i = start; i <= end; i++) {
            if (i >= 0 && i <= maxValue) {
              numbers.push(i);
            }
          }
        } else {
          // Cross-midnight/wraparound range (e.g., 22-4 for hours, 30-29 for minutes)
          for (let i = start; i <= maxValue; i++) {
            numbers.push(i);
          }
          for (let i = 0; i <= end; i++) {
            numbers.push(i);
          }
        }
      }
    } else {
      // Single number
      const num = parseInt(part.trim());
      if (!isNaN(num) && num >= 0 && num <= maxValue) {
        numbers.push(num);
      }
    }
  }

  return [...new Set(numbers)].sort((a, b) => a - b);
}

/**
 * Group consecutive numbers into ranges
 * @param {Array} numbers - Array of numbers
 * @returns {Array} Array of range objects {start, end}
 */
function groupConsecutiveNumbers(numbers) {
  if (numbers.length === 0) return [];

  const ranges = [];
  let start = numbers[0];
  let end = numbers[0];

  for (let i = 1; i < numbers.length; i++) {
    if (numbers[i] === end + 1) {
      // Consecutive number
      end = numbers[i];
    } else {
      // Gap found, save current range and start new one
      ranges.push({ start, end });
      start = numbers[i];
      end = numbers[i];
    }
  }

  // Add the last range
  ranges.push({ start, end });

  return ranges;
}

async function getBatchRecentOccurrencesFromRedis(alerts) {
  const statefulAlerts = alerts.filter(alert => alert.isStateful);
  if (statefulAlerts.length === 0) return null;

  try {
    const keys = statefulAlerts.flatMap(alert => [
      `alert_occurrence:${alert.siteId}:incident:${alert.incidentId}:count`,
      `alert_occurrence:${alert.siteId}:incident:${alert.incidentId}:ts`
    ]);

    const results = await RedisClient.mget(keys);

    if (!Array.isArray(results) || results.length !== keys.length) {
      sails.log.error('Redis mget returned invalid results', {
        expectedLength: keys.length,
        actualLength: results?.length
      });
      return null;
    }

    return statefulAlerts.reduce((acc, alert, index) => {
      const baseIndex = index * 2;
      const count = results[baseIndex];
      const recentTs = results[baseIndex + 1];

      acc[alert.incidentId] = {
        count: count ? parseInt(count) : null,
        recentTs: recentTs || null
      };
      return acc;
    }, {});
  } catch (error) {
    sails.log.error(`Error batch fetching from Redis: ${error.message}`);
    return null;
  }
}

module.exports = {
  /**
   * Acknowledge an alert incident
   * @param {string} userId - User ID acknowledging the alert
   * @param {string} siteId - Site ID where the alert belongs
   * @param {number} alertId - Alert inventory ID
   * @returns {Promise<Object>} Updated alert incident
   */
  acknowledge: async function (userId, siteId, alertId) {
    const alertInventory = await AlertInventory.findOne({
      id: alertId,
      siteId
    });

    if (!alertInventory) {
      throw flaverr({
        code: 'E_NOT_FOUND',
        message: `Alert with id ${alertId} not found for site ${siteId}`
      });
    }

    const subscription = await AlertSubscribers.findOne({
      alert_id: alertId,
      subscriber_id: userId,
      status: 1
    });

    if (!subscription) {
      throw flaverr({
        code: 'E_NOT_SUBSCRIBED',
        message: `User is not subscribed to alert ${alertId}`
      });
    }

    const activeIncident = await alertIncidentHistoryService.findOne({
      alert_inventory_id: alertId,
      issue_resolved_at: null
    });

    if (!activeIncident) {
      throw flaverr({
        code: 'E_NOT_FOUND',
        message: `No active incident found for alert ${alertId}`
      });
    }

    if (activeIncident.acknowledge_by || activeIncident.acknowledge_ts) {
      throw flaverr({
        code: 'E_ALREADY_ACKNOWLEDGED',
        message: `Alert incident is already acknowledged by ${activeIncident.acknowledge_by}`
      });
    }

    if (alertInventory.escalation_time_in_min) {
      const currentTime = moment().tz('UTC');
      const escalationThreshold = moment(activeIncident.issue_occurred_at).tz('UTC')
        .add(alertInventory.escalation_time_in_min, 'minutes');

      if (currentTime.isAfter(escalationThreshold)) {
        throw flaverr({
          code: 'E_ALREADY_ESCALATED',
          message: `Alert has already been escalated and cannot be acknowledged`
        });
      }
    }

    const acknowledgeTs = moment().tz('UTC').toDate();
    return await alertIncidentHistoryService.updateOne(
      { id: activeIncident.id },
      {
        acknowledge_by: userId,
        acknowledge_ts: acknowledgeTs
      }
    );
  },

  /**
   * List alerts with their details
   * @param {Object} params - Parameters for listing alerts
   * @returns {Promise<Array>} List of alerts
   */
  listAlerts: async function (params) {
    const {
      siteId,
      startTime,
      endTime,
      alertStatus = 'active',
      subscriberId,
      assetType,
      severity,
      durationFilter,
      search,
      sortBy = 'occurredAt',
      sortOrder = 'desc',
      assetId
    } = params;

    const queryParams = [startTime, endTime, siteId];
    let paramCount = 4;

    let query = `
      SELECT
        aih.id as incident_id,
        aih.issue_occurred_at,
        aih.issue_resolved_at,
        aih.acknowledge_by,
        aih.acknowledge_ts,
        aih.occurred_event_count,
        aih.recent_occurred_event_ts,
        CASE
          WHEN aih.issue_resolved_at IS NULL THEN 1
          ELSE 0
        END AS is_stateful,
        CASE
          WHEN aih.acknowledge_by IS NOT NULL AND aih.acknowledge_ts IS NOT NULL THEN 1
          ELSE 0
        END AS is_acknowledged,
        incident_summary.total_duration,
        incident_summary.duration_percentage,
        ai.id as alert_id,
        ai.name as alert_name,
        ai.description,
        ai.severity,
        ai.asset_id as device_id,
        ai.asset_type as device_type,
        ai.siteid as site_id,
        ai.escalation_time_in_min,
        ai.escalated_to,
        at.id as alert_group_id,
        at.alert_category as alert_type,
        at.observer_execution_ref_id as observer_execution_ref_id
      FROM
        alert_incident_history aih
      JOIN (
        SELECT
          aih.alert_inventory_id,
          ROUND(
            LEAST(
              SUM(EXTRACT(EPOCH FROM (
                LEAST(COALESCE(aih.issue_resolved_at, $2), $2) - GREATEST(aih.issue_occurred_at, $1)
              ))
            ) / 60,
              EXTRACT(EPOCH FROM ($2 - $1))/60
            )
          ) AS total_duration,
          ROUND(
            LEAST(
              (SUM(
                EXTRACT(EPOCH FROM (
                  LEAST(COALESCE(aih.issue_resolved_at, $2), $2) - GREATEST(aih.issue_occurred_at, $1)
                ))
              ) * 100.0) /
              EXTRACT(EPOCH FROM ($2 - $1)),
              100
            )
          ) AS duration_percentage,
          MAX(aih.id) AS latest_incident_id
        FROM
          alert_incident_history aih
        JOIN
          alert_inventory ai ON ai.id = aih.alert_inventory_id
        JOIN
          alert_template at ON at.id = ai.alert_template_ref_id
        WHERE
          ai.siteid = $3
          AND at.status = 1
          AND aih.issue_occurred_at <= $2
          AND (aih.issue_resolved_at IS NULL OR aih.issue_resolved_at >= $1)
        GROUP BY
          aih.alert_inventory_id
      ) incident_summary ON incident_summary.latest_incident_id = aih.id
      JOIN alert_inventory ai ON ai.id = aih.alert_inventory_id
      JOIN alert_template at ON at.id = ai.alert_template_ref_id
      WHERE
        ai.siteid = $3
    `;

    if (alertStatus !== 'all') {
      if (alertStatus === 'active') {
        query += ` AND aih.issue_resolved_at IS NULL`;
      } else if (alertStatus === 'resolved') {
        query += ` AND aih.issue_resolved_at IS NOT NULL`;
      }
    }

    if (search) {
      query += ` AND (
        ai.name ILIKE $${paramCount}
        OR CAST(ai.id AS TEXT) LIKE $${paramCount}
      )`;
      queryParams.push(`%${search}%`);
      paramCount++;
    }

    if (subscriberId) {
      query += ` AND EXISTS (
        SELECT 1 FROM alert_subscribers
        WHERE alert_id = ai.id
        AND subscriber_id = $${paramCount}
        AND status = 1
      )`;
      queryParams.push(subscriberId);
      paramCount++;
    }

    if (assetType) {
      const assetTypes = assetType.split(',');
      query += ` AND ai.asset_type IN (${assetTypes.map((_, index) =>
        `$${paramCount + index}`).join(',')})`;
      queryParams.push(...assetTypes);
      paramCount += assetTypes.length;
    }

    if (assetId) {
      const assetIds = assetId.split(','); // Support multiple assetIds
      query += ` AND ai.asset_id IN (${assetIds.map((_, index) =>
        `$${paramCount + index}`).join(',')})`;
      queryParams.push(...assetIds);
      paramCount += assetIds.length;
    }

    if (severity) {
      const severities = severity.split(',');
      query += ` AND ai.severity IN (${severities.map((_, index) =>
        `$${paramCount + index}`).join(',')})`;
      queryParams.push(...severities);
      paramCount += severities.length;
    }

    if (durationFilter) {
      const filters = durationFilter.split(',');
      const durationConditions = [];

      if (filters.includes('long')) {
        durationConditions.push('incident_summary.duration_percentage > 75');
      }

      if (filters.includes('medium')) {
        durationConditions.push('incident_summary.duration_percentage BETWEEN 25 AND 75');
      }

      if (filters.includes('short')) {
        durationConditions.push('incident_summary.duration_percentage < 25');
      }

      if (durationConditions.length > 0) {
        query += ` AND (${durationConditions.join(' OR ')})`;
      }
    }

    let orderByClause = '';
    switch (sortBy) {
      case 'name':
        orderByClause = `ai.name ${sortOrder}, aih.issue_occurred_at DESC`;
        break;

      case 'severity':
        orderByClause = `
          CASE ai.severity
            WHEN 'critical' THEN 1
            WHEN 'high' THEN 2
            WHEN 'medium' THEN 3
            WHEN 'low' THEN 4
            ELSE 5
          END ${sortOrder === 'desc' ? 'ASC' : 'DESC'},
          aih.issue_occurred_at DESC`;
        break;

      case 'duration':
        orderByClause = `incident_summary.total_duration ${sortOrder}, aih.issue_occurred_at DESC`;
        break;

      case 'occurredAt':
        orderByClause = `aih.issue_occurred_at ${sortOrder}`;
        break;

      case 'recentOccurrence':
      default:
        orderByClause = `aih.recent_occurred_event_ts ${sortOrder} NULLS LAST, aih.issue_occurred_at DESC`;
    }

    query += ` ORDER BY ${orderByClause}`;

    const rawAlerts = await sails.getDatastore(process.env.SMART_ALERT_DB_NAME).sendNativeQuery(query, queryParams);

    const alertIds = rawAlerts.rows.map(alert => alert.alert_id);
    const subscribers = await AlertSubscribers.find({
      where: { alert_id: { in: alertIds }, status: 1 },
      select: ['alert_id', 'subscriber_id']
    });

    const subscribersMap = subscribers.reduce((map, subscriber) => {
      if (!map[subscriber.alert_id]) {
        map[subscriber.alert_id] = [];
      }
      map[subscriber.alert_id].push(subscriber.subscriber_id);
      return map;
    }, {});

    const alerts = rawAlerts.rows.map(alert => {
      const stringTemplateReplacementMap = {
        paramMap: {
          component: {
            id: alert.device_id,
          },
          site: {
            id: siteId
          }
        }
      }
      return {
        incidentId: alert.incident_id,
        alertId: alert.alert_id,
        alertGroupId: alert.alert_group_id,
        alertType: alert.alert_type,
        siteId: alert.site_id,
        observedOnAsset: {
          deviceId: alert.device_id,
          deviceType: alert.device_type,
        },
        alertName: alert.alert_name && alert.alert_name.trim() !== ''
          ? sails.helpers.globalParamFormatter.with({ string: alert.alert_name, stringTemplateReplacementMap })
          : alert.alert_name || '',
        description: alert.description && alert.description.trim() !== ''
          ? sails.helpers.globalParamFormatter.with({ string: alert.description, stringTemplateReplacementMap })
          : alert.description || '',
        severity: alert.severity,
        subscriberList: subscribersMap[alert.alert_id] || [],
        lastOccurredAt: moment(alert.issue_occurred_at).tz('UTC').toISOString(),
        observerExecutionRefId: alert.observer_execution_ref_id,
        isStateful: alert.is_stateful,
        isAcknowledged: alert.is_acknowledged,
        activeDuration: alert.total_duration || 0,
        durationPercentage: alert.duration_percentage || 0,
        escalationTimeInMin: alert.escalation_time_in_min,
        escalatedTo: alert.escalated_to || [],
        acknowledgeBy: alert.acknowledge_by,
        acknowledgeTimestamp: alert.acknowledge_ts,
        occurredEventCount: parseInt(alert.occurred_event_count || 1),
        recentOccurredEventTs: alert.recent_occurred_event_ts ?
          moment(alert.recent_occurred_event_ts).tz('UTC').toISOString() : null
      };
    });

    // After getting the base alerts, update the stateful ones with Redis data
    const redisData = await getBatchRecentOccurrencesFromRedis(alerts);
    alerts.forEach(alert => {
      if (alert.isStateful && redisData) {
        const redisValues = redisData[alert.incidentId];
        if (redisValues?.count) {
          alert.occurredEventCount = redisValues.count;
        }
        if (redisValues?.recentTs) {
          alert.recentOccurredEventTs = moment(redisValues.recentTs).tz('UTC').toISOString();
        }
      }
    });

    if (sortBy === 'recentOccurrence') {
      alerts.sort((a, b) => {
        const timeA = a.recentOccurredEventTs || a.lastOccurredAt;
        const timeB = b.recentOccurredEventTs || b.lastOccurredAt;
        return sortOrder === 'desc'
          ? moment(timeB).valueOf() - moment(timeA).valueOf()
          : moment(timeA).valueOf() - moment(timeB).valueOf();
      });
    }

    return alerts;
  },

  /**
   * Get alert incident details by alert inventory ID
   * @param {number} alertInventoryId - Alert inventory ID
   * @returns {Promise<Object>} Alert incident details
   */
  getAlertCurrentState: async function (alertInventoryId) {
    const query = `
      SELECT
        aih.id as incident_id,
        aih.issue_occurred_at,
        aih.issue_resolved_at,
        aih.occurred_event_count,
        aih.recent_occurred_event_ts,
        aih.block_id,
        CASE
          WHEN aih.issue_resolved_at IS NULL THEN 1
          ELSE 0
        END AS is_stateful,
        ai.siteid as site_id
      FROM
        alert_incident_history aih
      JOIN
        alert_inventory ai ON ai.id = aih.alert_inventory_id
      JOIN
        alert_template at ON at.id = ai.alert_template_ref_id
      WHERE
        aih.alert_inventory_id = $1
        AND at.status = 1
      ORDER BY
        aih.recent_occurred_event_ts DESC NULLS LAST,
        aih.issue_occurred_at DESC
      LIMIT 1
    `;

    const rawResult = await sails.getDatastore(process.env.SMART_ALERT_DB_NAME).sendNativeQuery(query, [alertInventoryId]);

    if (!rawResult.rows || rawResult.rows.length === 0) {
      return {
        incidentId: null,
        issueOccurredAt: null,
        issueResolvedAt: null,
        recentOccurredEventTs: null,
        occurredEventCount: 0,
        blockId: null
      };
    }

    const incident = rawResult.rows[0];
    const isStateful = incident.is_stateful === 1;
    const issueOccurredAt = moment(incident.issue_occurred_at).tz('UTC').toISOString();

    let result = {
      incidentId: incident.incident_id,
      issueOccurredAt,
      issueResolvedAt: incident.issue_resolved_at ?
        moment(incident.issue_resolved_at).tz('UTC').toISOString() : null,
      recentOccurredEventTs: incident.recent_occurred_event_ts ?
        moment(incident.recent_occurred_event_ts).tz('UTC').toISOString() : issueOccurredAt,
      occurredEventCount: parseInt(incident.occurred_event_count || 1),
      blockId: incident.block_id
    };

    if (isStateful) {
      const alertForRedis = {
        incidentId: incident.incident_id,
        siteId: incident.site_id,
        isStateful: true
      };

      const redisData = await getBatchRecentOccurrencesFromRedis([alertForRedis]);

      if (redisData && redisData[incident.incident_id]) {
        const redisValues = redisData[incident.incident_id];

        if (redisValues.count) {
          result.occurredEventCount = redisValues.count;
        }

        if (redisValues.recentTs && moment(redisValues.recentTs).isValid()) {
          result.recentOccurredEventTs = moment(redisValues.recentTs).tz('UTC').toISOString();
        }
      }
    }

    return result;
  },

  /**
   * Fetch recipe metadata for a given blockId
   * @param {number} blockId - ID of the child recipe block
   * @returns {Promise<Object|null>} Recipe metadata or null if not found
   */
  getRecipeDetailsByBlockId: async function (blockId) {
    if (!blockId) {
      return null;
    }
    const recipeDb = sails.getDatastore('postgres');
    const query = `
      SELECT
        cr.formula,
        cr.expression_template,
        cr.operators,
        cr.params,
        ri.components_type,
        ri.recipe_category,
        ri.run_interval,
        ri.run_on,
        cr.parent_recipe_id
      FROM
        children_recipes cr
      JOIN
        recipe_info ri ON ri.id = cr.parent_recipe_id
      WHERE
        cr.uniqid = $1
      LIMIT 1
    `;
    const result = await recipeDb.sendNativeQuery(query, [blockId]);
    if (!result.rows || result.rows.length === 0) {
      return null;
    }
    const {
      formula,
      expression_template,
      operators,
      params,
      components_type,
      recipe_category,
      run_interval,
      run_on,
      parent_recipe_id
    } = result.rows[0];
    // fetch schedules
    const scheduleQuery = `
      SELECT
        uniqid AS sid,
        repeat_type,
        start_date,
        end_date,
        cron,
        status,
        time_ranges
      FROM recipe_schedule
      WHERE recipe_id = $1
    `;
    const scheduleResult = await recipeDb.sendNativeQuery(scheduleQuery, [parent_recipe_id]);
    // map schedules
    const schedules = scheduleResult.rows.map(r => ({
      sid: r.sid,
      repeat_type: r.repeat_type,
      start_date: moment.tz(r.start_date, 'UTC').format('YYYY-MM-DD HH:mm'),
      end_date: moment.tz(r.end_date, 'UTC').format('YYYY-MM-DD HH:mm'),
      time_ranges:
        r.time_ranges && r.time_ranges.length > 0
          ? r.time_ranges
          : extractTimeRangesFromCron(r.cron),
      isDeployed: String(r.status),
    }));
    return {
      formula,
      expressionTemplate: expression_template,
      operators,
      params,
      componentsType: components_type,
      recipelabel: [recipe_category],
      runInterval: run_interval,
      runOn: run_on,
      schedules,
      recipeInfoId: parent_recipe_id
    };
  }
};
