module.exports = function(req, res, next) {
  if (!req.headers.authorization)
      return res.badRequest({
        message: "Missing authorization header"
      });
  const hash = "ef6c6bc07282138f3ff960dbfc57f7d2";
  let authH = req.headers.authorization;
  let reqHash = authH.split(" ");
  if (reqHash.length != 2 || reqHash[1] != hash)
      return res.badRequest({
          message: "Invalid authorization header"
      });
  next();
};