import { v4 as uuidv4 } from 'uuid';
import moment from 'moment-timezone';
import logger from '../utils/logger';
import { DynamoDB } from '../repository/dynamodb';
import { ChannelNotificationMessage } from '../interface/channel-notification-message';
import { NotificationPayload } from '../interface/notification-payload';
import postgres from '../connection/postgres';
import { CacheService, CACHE_TTL } from '../utils/cache';
import RedisConnection from '../connection/redis';
import recipeRepository from '../repository/recipe';

export class MessagePayloadBuilder {
  private channelWiseSubscriberDetails: Record<string, ChannelNotificationMessage> = {};
  private readonly dynamoDB = new DynamoDB();
  private readonly cache = CacheService.getInstance();
  private isRecipeAlert = false;
  private blockId: string | null = null;
  private childRecipe: any = null;

  constructor(private readonly payload: NotificationPayload) {
    this.initializeChannelDetails();
  }

  public async build(): Promise<Record<string, ChannelNotificationMessage>> {
    try {
      this.isRecipeAlert = this.payload.ALERT_CATEGORY === 'recipe';

      if (this.isRecipeAlert) {
        this.blockId = await recipeRepository.getBlockIdForIncidentId(this.payload.incidentId);

        if (this.blockId) {
          this.childRecipe = await recipeRepository.getChildRecipeByBlockId(this.blockId);
        }
      }

      const alertSubscribers = await this.fetchAlertSubscribers();
      if (!alertSubscribers?.length) {
        return this.channelWiseSubscriberDetails;
      }

      const [templates, assetDetails, siteName] = await Promise.all([
        this.fetchNotificationTemplates(),
        this.fetchAssetDetails(),
        this.fetchSiteName(),
      ]);

      await this.populateChannelDetails({
        alertSubscribers,
        templates,
        assetDetails,
        siteName,
      });

      await this.filterSubscribersByGlobalPreferences();

      return this.channelWiseSubscriberDetails;
    } catch (error) {
      error.message = `Failed to build message payload: ${error.message}`;
      throw error;
    }
  }

  private async fetchAlertSubscribers() {
    const cacheKey = `alert:subscribers:${this.payload.alertInventoryId}`;

    return this.cache.getFromCacheOrDB(
      cacheKey,
      async () => {
        const query = `
        SELECT DISTINCT subscriber_id, notify_on_email, notify_on_whatsapp, notify_on_sms
        FROM alert_subscribers
        WHERE alert_id = $1
          AND status = 1
          AND (paused_till IS NULL OR paused_till AT TIME ZONE 'UTC' < CURRENT_TIMESTAMP AT TIME ZONE 'UTC')
      `;
        const result = await postgres.query(query, [this.payload.alertInventoryId]);
        return result.rows;
      },
      CACHE_TTL.SUBSCRIBERS,
    );
  }

  private async fetchNotificationTemplates() {
    if (this.isRecipeAlert && this.childRecipe) {
      logger.info('Using child-specific description for recipe alert', {
        alertTemplateId: this.payload.alertTemplateId,
        blockId: this.blockId,
      });

      // Create custom templates for each channel
      const customTemplates = [];
      ['email', 'whatsapp', 'sms'].forEach((channel) => {
        customTemplates.push({
          channel,
          description: this.childRecipe.description || this.childRecipe.parent_description,
          title: this.childRecipe.parent_title,
          template_id: null,
        });
      });

      return customTemplates;
    }

    const cacheKey = `alert:templates:${this.payload.alertTemplateId}:${this.payload.eventName}`;

    return this.cache.getFromCacheOrDB(
      cacheKey,
      async () => {
        const query = `
        SELECT template_id, title, description, channel
        FROM notification_message_template
        WHERE alert_template_ref_id = $1
          AND status = 1
          AND event_type = $2
      `;
        let result = await postgres.query(query, [this.payload.alertTemplateId, this.payload.eventName]);

        // If no templates found for RESOLVED or REMINDER event, fallback to OCCURRED templates
        if ((this.payload.eventName === 'RESOLVED' || this.payload.eventName === 'REMINDER') && !result.rows.length) {
          logger.info({
            message: `No ${this.payload.eventName} templates found, falling back to OCCURRED templates`,
            alertTemplateId: this.payload.alertTemplateId,
          });

          result = await postgres.query(query, [this.payload.alertTemplateId, 'OCCURRED']);
        }

        const templates = result.rows;
        const availableChannels = new Set(templates.map((t: any) => t.channel));
        const requiredChannels = ['email', 'whatsapp', 'sms'];

        // Check if any required channels are missing templates
        const missingChannels = requiredChannels.filter((channel) => !availableChannels.has(channel));

        if (missingChannels.length > 0) {
          logger.info({
            message: `Missing templates for channels: ${missingChannels.join(', ')}, fetching alert template fallback`,
            alertTemplateId: this.payload.alertTemplateId,
            missingChannels,
          });

          const alertTemplateQuery = `
            SELECT name, description
            FROM alert_template
            WHERE id = $1
              AND status = 1
          `;

          const alertTemplateResult = await postgres.query(alertTemplateQuery, [this.payload.alertTemplateId]);

          if (alertTemplateResult.rows.length > 0) {
            const alertTemplate = alertTemplateResult.rows[0] as { name: string; description: string };

            missingChannels.forEach((channel) => {
              templates.push({
                channel,
                title: alertTemplate.name,
                description: alertTemplate.description,
                template_id: null,
              });
            });
          } else {
            logger.warn({
              message: `Alert template not found for fallback`,
              alertTemplateId: this.payload.alertTemplateId,
            });
          }
        }

        return templates;
      },
      CACHE_TTL.TEMPLATES,
    );
  }

  private async fetchUserDetails(userId: string) {
    const cacheKey = `user:details:${userId}`;

    return this.cache.getFromCacheOrDB(
      cacheKey,
      async () => {
        return this.dynamoDB.getUserDetailById(userId);
      },
      CACHE_TTL.USER_DETAILS,
    );
  }

  private async fetchAssetDetails() {
    const cacheKey = `asset:details:${this.payload.assetId}`;

    return this.cache.getFromCacheOrDB(
      cacheKey,
      async () => {
        return this.dynamoDB.getAssetDetails(this.payload.assetId);
      },
      CACHE_TTL.ASSET_DETAILS,
    );
  }

  private async fetchSiteName() {
    const cacheKey = `site:name:${this.payload.siteId}`;

    return this.cache.getFromCacheOrDB(
      cacheKey,
      async () => {
        return this.dynamoDB.getSiteName(this.payload.siteId);
      },
      CACHE_TTL.SITE_DETAILS,
    );
  }

  private initializeChannelDetails(): void {
    const defaultMetadata = {
      incidentId: this.payload.incidentId,
      alertInventoryId: this.payload.alertInventoryId,
      eventName: this.payload.eventName,
      deviceId: this.payload.assetId,
      siteId: this.payload.siteId,
      severity: this.payload.severity,
      alertType: this.payload.ALERT_CATEGORY,
      observer_execution_ref_id: this.payload.observer_execution_ref_id,
      timestamp: this.payload.timestamp,
      ...(this.payload.eventName === 'RESOLVED' || this.payload.eventName === 'REMINDER'
        ? { timestampOccurred: this.payload.timestampOccurred }
        : {}),
    };

    ['email', 'whatsapp', 'sms'].forEach((channel) => {
      this.channelWiseSubscriberDetails[channel] = {
        recipients: [],
        content: { title: '', body: '' },
        metadata: { ...defaultMetadata },
        channel,
        ts: moment().tz('UTC').toISOString(),
        transactionId: this.payload.transactionId || uuidv4(),
      };
    });
  }

  private async populateChannelDetails({
    alertSubscribers,
    templates,
    assetDetails,
    siteName,
  }: {
    alertSubscribers: any[];
    templates: any[];
    assetDetails: { name: string; type: string };
    siteName: string;
  }): Promise<void> {
    const subscriberDetails = await Promise.all(
      alertSubscribers.map((subscriber) => this.fetchUserDetails(subscriber.subscriber_id)),
    );

    alertSubscribers.forEach((subscriber, index) => {
      const details = subscriberDetails[index]?.[0];
      if (details) {
        this.addSubscriberToChannel(subscriber, details);
      }
    });

    templates.forEach((template) => {
      const channel = this.channelWiseSubscriberDetails[template.channel];
      if (channel) {
        Object.assign(channel, {
          content: {
            body: template.description,
            title: template.title,
          },
          metadata: {
            ...channel.metadata,
            templateId: template.template_id,
            assetName: assetDetails.name,
            deviceType: assetDetails.type,
            siteName,
          },
        });
      }
    });
  }

  private addSubscriberToChannel(subscriber: any, details: any): void {
    const recipientInfo = {
      userId: details.userId,
      name: details.name,
      email: details.userId,
      whatsappNumber: details.whatsappNumber,
      mobileNumber: details.phone,
    };

    // Helper function to check if recipient already exists in channel
    const isRecipientAlreadyAdded = (recipients: any[], userId: string): boolean => {
      return recipients.some(recipient => recipient.userId === userId);
    };

    if (subscriber.notify_on_email && !isRecipientAlreadyAdded(this.channelWiseSubscriberDetails.email.recipients, details.userId)) {
      this.channelWiseSubscriberDetails.email.recipients.push(recipientInfo);
    }
    if (subscriber.notify_on_whatsapp && !isRecipientAlreadyAdded(this.channelWiseSubscriberDetails.whatsapp.recipients, details.userId)) {
      this.channelWiseSubscriberDetails.whatsapp.recipients.push(recipientInfo);
    }
    if (subscriber.notify_on_sms && !isRecipientAlreadyAdded(this.channelWiseSubscriberDetails.sms.recipients, details.userId)) {
      this.channelWiseSubscriberDetails.sms.recipients.push(recipientInfo);
    }
  }

  private async getBatchGlobalNotificationPreferencesBySiteId(
    userIds: string[],
    siteId: string,
  ): Promise<Record<string, Record<string, number>>> {
    if (!userIds.length) {
      return {};
    }

    const redis = RedisConnection.getInstance();
    const userPrefs: Record<string, Record<string, number>> = {};

    const keys = userIds.map((userId) => `userSiteMap:${siteId}:${userId}:notificationPref`);

    try {
      const cachedResults = await redis.mget(keys);

      const missingUserIds: string[] = [];

      userIds.forEach((userId, index) => {
        const cachedValue = cachedResults[index];

        if (cachedValue) {
          try {
            userPrefs[userId] = JSON.parse(cachedValue);
          } catch (error) {
            logger.warn(`Failed to parse cached notification preferences`, { userId, siteId, error });
            missingUserIds.push(userId);
          }
        } else {
          missingUserIds.push(userId);
        }
      });

      if (missingUserIds.length > 0) {
        const dbFetchPromises = missingUserIds.map(async (userId) => {
          try {
            const prefs = await this.dynamoDB.getUserNotificationPreferences(userId, siteId);
            userPrefs[userId] = prefs;

            await redis.set(
              `userSiteMap:${siteId}:${userId}:notificationPref`,
              JSON.stringify(prefs),
              CACHE_TTL.NOTIFICATION_PREFERENCES,
            );

            return { userId, prefs };
          } catch (error) {
            logger.error(`Failed to fetch notification preferences`, { userId, siteId, error });
            userPrefs[userId] = {
              email: 1,
              sms: 1,
              whatsapp: 1,
            };
            return { userId, prefs: userPrefs[userId] };
          }
        });

        await Promise.all(dbFetchPromises);
      }

      return userPrefs;
    } catch (error) {
      logger.error('Failed to fetch batch user notification preferences', { error });

      userIds.forEach((userId) => {
        userPrefs[userId] = {
          email: 1,
          sms: 1,
          whatsapp: 1,
        };
      });

      return userPrefs;
    }
  }

  private async filterSubscribersByGlobalPreferences(): Promise<void> {
    const allUserIds = new Set<string>();

    for (const channelDetails of Object.values(this.channelWiseSubscriberDetails)) {
      if (!channelDetails.recipients.length) continue;

      channelDetails.recipients.forEach((recipient) => {
        if (recipient.userId) {
          allUserIds.add(recipient.userId);
        }
      });
    }

    if (allUserIds.size === 0) return;

    const userIds = Array.from(allUserIds);
    const userPreferences = await this.getBatchGlobalNotificationPreferencesBySiteId(userIds, this.payload.siteId);

    for (const [channel, details] of Object.entries(this.channelWiseSubscriberDetails)) {
      if (!details.recipients.length) {
        continue;
      }

      const filteredRecipients = [];

      for (const recipient of details.recipients) {
        const userId = recipient.userId;

        if (userPreferences[userId] && userPreferences[userId][channel] == 0) {
          logger.debug(`Skipping notification for userId=${userId} on channel=${channel} due to global preferences`);
        } else {
          filteredRecipients.push(recipient);
        }
      }

      details.recipients = filteredRecipients;
    }
  }
}
